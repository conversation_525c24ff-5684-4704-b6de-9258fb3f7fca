/* 🌟 Enhanced Recipient Details Modal - Ultra Modern Design 🌟 */

/* Stunning Modal Overlay with Dynamic Gradient */
.recipient-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(99, 102, 241, 0.95),
    rgba(168, 85, 247, 0.95),
    rgba(236, 72, 153, 0.95),
    rgba(59, 130, 246, 0.95));
  backdrop-filter: blur(20px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: fadeInOverlay 0.6s ease-out;
}

@keyframes fadeInOverlay {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(20px);
  }
}

/* Glass Morphism Modal Container */
.recipient-modal-container {
  background: linear-gradient(145deg,
    rgba(255, 255, 255, 0.95),
    rgba(255, 255, 255, 0.9));
  backdrop-filter: blur(30px);
  border-radius: 24px;
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideUpScale 0.7s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
}

@keyframes slideUpScale {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9) rotateX(15deg);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1) rotateX(0deg);
  }
}

/* Enhanced Header with Stunning Gradient */
.recipient-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32px;
  background: linear-gradient(135deg,
    #667eea 0%,
    #764ba2 25%,
    #f093fb 50%,
    #f5576c 75%,
    #4facfe 100%);
  border-radius: 24px 24px 0 0;
  position: relative;
  overflow: hidden;
}

/* Animated Background Pattern */
.header-background-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="sparkles" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="3" cy="3" r="1" fill="rgba(255,255,255,0.3)"/><circle cx="15" cy="10" r="0.5" fill="rgba(255,255,255,0.5)"/><circle cx="10" cy="15" r="0.8" fill="rgba(255,255,255,0.2)"/></pattern></defs><rect width="100" height="100" fill="url(%23sparkles)"/></svg>');
  opacity: 0.6;
  animation: sparkleMove 15s linear infinite;
}

@keyframes sparkleMove {
  0% { transform: translateX(0) translateY(0); }
  100% { transform: translateX(-20px) translateY(-20px); }
}

.recipient-modal-profile {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  z-index: 1;
}

/* Enhanced Avatar with Glow Effect */
.recipient-modal-avatar-wrapper {
  position: relative;
}

.recipient-modal-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 28px;
  color: white;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: 4px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
}

.avatar-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  animation: avatarGlow 3s ease-in-out infinite;
}

@keyframes avatarGlow {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.1); }
}

.status-indicator {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #10b981, #059669);
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Enhanced Profile Info */
.recipient-modal-info {
  position: relative;
  z-index: 1;
}

.recipient-modal-name {
  font-size: 28px;
  font-weight: 800;
  color: white;
  margin: 0 0 8px 0;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  animation: titleGlow 3s ease-in-out infinite;
}

@keyframes titleGlow {
  0%, 100% {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }
  50% {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255, 255, 255, 0.5);
  }
}

.recipient-modal-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  color: rgba(255, 255, 255, 0.95);
  font-weight: 600;
  margin: 0 0 6px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.title-icon {
  width: 18px;
  height: 18px;
  color: #fbbf24;
}

.recipient-modal-department {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  margin: 0 0 12px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.department-icon {
  width: 16px;
  height: 16px;
  color: #60a5fa;
}

/* Profile Badges */
.profile-badges {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.profile-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-shadow: none;
}

.profile-badge.verified {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.profile-badge.top-performer {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

/* Enhanced Close Button */
.recipient-modal-close {
  background: rgba(255, 255, 255, 0.25);
  border: none;
  cursor: pointer;
  padding: 16px;
  border-radius: 50%;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.recipient-modal-close:hover {
  background: rgba(255, 255, 255, 0.4);
  transform: rotate(90deg) scale(1.15);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.close-icon {
  width: 24px;
  height: 24px;
  color: white;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* Enhanced Stats Overview with Colorful Cards */
.recipient-modal-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding: 32px;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  position: relative;
}

.stat-item {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px 16px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-item:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.stat-item.points {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
}

.stat-item.badges {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.stat-item.rank {
  background: linear-gradient(135deg, #ec4899, #db2777);
}

.stat-item.monthly {
  background: linear-gradient(135deg, #10b981, #059669);
}

.stat-icon-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 12px;
}

.stat-icon {
  width: 32px;
  height: 32px;
  color: white;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.stat-content {
  position: relative;
  z-index: 1;
}

.stat-number {
  font-size: 32px;
  font-weight: 800;
  color: white;
  margin-bottom: 6px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.stat-sparkle {
  position: absolute;
  top: 12px;
  right: 12px;
  font-size: 20px;
  animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
  0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.7; }
  50% { transform: scale(1.2) rotate(180deg); opacity: 1; }
}

/* Enhanced Content */
.recipient-modal-content {
  padding: 32px;
  background: linear-gradient(135deg, #f8fafc, #ffffff);
}

.modal-section {
  margin-bottom: 40px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
}

.modal-section:last-child {
  margin-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 22px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 20px;
  position: relative;
}

.section-icon {
  width: 24px;
  height: 24px;
  color: #667eea;
}

.section-count {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  margin-left: auto;
}

.trend-indicator {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  margin-left: auto;
}

/* Enhanced Achievements Grid */
.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.achievement-card {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.achievement-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.achievement-card.gold {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
}

.achievement-card.blue {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
}

.achievement-card.pink {
  background: linear-gradient(135deg, #ec4899, #db2777);
  color: white;
}

.achievement-card.green {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.achievement-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.achievement-icon-wrapper {
  position: relative;
}

.achievement-card-icon {
  width: 32px;
  height: 32px;
  color: white;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.achievement-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  animation: achievementGlow 3s ease-in-out infinite;
}

@keyframes achievementGlow {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.1); }
}

.achievement-level {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 600;
}

.achievement-card-name {
  font-size: 16px;
  font-weight: 700;
  margin: 0 0 6px 0;
}

.achievement-card-desc {
  font-size: 13px;
  opacity: 0.9;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.achievement-progress {
  margin-top: 12px;
}

.progress-bar {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  height: 6px;
  overflow: hidden;
  margin-bottom: 6px;
}

.progress-fill {
  background: rgba(255, 255, 255, 0.8);
  height: 100%;
  border-radius: 10px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 11px;
  font-weight: 600;
  opacity: 0.9;
}

/* Recognitions List */
.recognitions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.recognition-card {
  padding: 16px;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.recognition-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.recognition-from {
  display: flex;
  align-items: center;
  gap: 8px;
}

.recognition-avatar {
  width: 32px;
  height: 32px;
  background-color: #4b381b;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: 600;
}

.recognition-author {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
}

.recognition-category {
  font-size: 12px;
  color: #19c55b;
  background-color: #eff6ff;
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 8px;
}

.recognition-date {
  font-size: 12px;
  color: #6b7280;
}

.recognition-message {
  font-size: 14px;
  color: #374151;
  line-height: 1.5;
  margin: 8px 0;
}

.recognition-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.recognition-likes {
  font-size: 12px;
  color: #6b7280;
}

/* Trend Chart */
.trend-chart {
  display: flex;
  align-items: end;
  gap: 12px;
  height: 120px;
  padding: 16px;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.trend-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  height: 100%;
  position: relative;
}

.trend-bar-fill {
  background: linear-gradient(to top, #96baf4, #26c8ec);
  width: 100%;
  border-radius: 4px 4px 0 0;
  min-height: 4px;
  margin-bottom: auto;
}

.trend-month {
  font-size: 12px;
  color: #6b7280;
  margin-top: 8px;
}

.trend-value {
  font-size: 10px;
  color: #374151;
  font-weight: 500;
  position: absolute;
  top: -20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .recipient-modal-overlay {
    padding: 10px;
  }
  
  .recipient-modal-container {
    max-height: 95vh;
  }
  
  .recipient-modal-header {
    padding: 16px;
  }
  
  .recipient-modal-profile {
    gap: 12px;
  }
  
  .recipient-modal-avatar {
    width: 48px;
    height: 48px;
    font-size: 18px;
  }
  
  .recipient-modal-stats {
    grid-template-columns: repeat(2, 1fr);
    padding: 16px;
  }
  
  .recipient-modal-content {
    padding: 16px;
  }
  
  .achievements-grid {
    grid-template-columns: 1fr;
  }
  
  .trend-chart {
    height: 100px;
  }
}
