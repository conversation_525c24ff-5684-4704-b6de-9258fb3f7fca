/* 🔔 Enhanced Notifications Dropdown with Attractive Colors */
.notifications-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: transparent;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.notifications-dropdown {
  position: absolute;
  top: 70px;
  right: 20px;
  width: 420px;
  max-height: 600px;
  background: linear-gradient(145deg,
    rgba(255, 255, 255, 0.95),
    rgba(255, 255, 255, 0.9));
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
  animation: slideDown 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced Header with Gradient */
.notifications-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  background: linear-gradient(135deg,
    #667eea 0%,
    #764ba2 50%,
    #f093fb 100%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.notifications-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="sparkles" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="5" cy="5" r="0.8" fill="rgba(255,255,255,0.3)"/><circle cx="15" cy="10" r="0.5" fill="rgba(255,255,255,0.4)"/><circle cx="10" cy="15" r="0.6" fill="rgba(255,255,255,0.2)"/></pattern></defs><rect width="100" height="100" fill="url(%23sparkles)"/></svg>');
  opacity: 0.3;
  animation: sparkleMove 15s linear infinite;
}

@keyframes sparkleMove {
  0% { transform: translateX(0) translateY(0); }
  100% { transform: translateX(-20px) translateY(-20px); }
}

.notifications-title {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  position: relative;
  z-index: 1;
}

.title-with-icon {
  display: flex;
  align-items: center;
  gap: 8px;
}

.bell-icon {
  width: 20px;
  height: 20px;
  color: white;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  animation: bellRing 2s ease-in-out infinite;
}

@keyframes bellRing {
  0%, 100% { transform: rotate(0deg); }
  10%, 30% { transform: rotate(-10deg); }
  20%, 40% { transform: rotate(10deg); }
}

.notifications-title h3 {
  font-size: 18px;
  font-weight: 700;
  color: white;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.unread-count {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
  font-size: 11px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(251, 191, 36, 0.4);
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.notifications-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  z-index: 1;
}

.mark-all-read-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.mark-all-read-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.check-icon {
  width: 14px;
  height: 14px;
}

.close-notifications-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.close-notifications-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: rotate(90deg) scale(1.1);
}

.close-icon {
  width: 16px;
  height: 16px;
  color: white;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

/* Filter Tabs */
.notification-filters {
  display: flex;
  padding: 16px 20px 0;
  gap: 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.5);
}

.filter-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.7);
  color: #6b7280;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.filter-btn:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  transform: translateY(-1px);
}

.filter-btn.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  transform: translateY(-1px);
}

/* Enhanced Notifications List */
.notifications-list {
  max-height: 400px;
  overflow-y: auto;
  padding: 8px 0;
}

.notifications-list::-webkit-scrollbar {
  width: 6px;
}

.notifications-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
}

.notifications-list::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 3px;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px 20px;
  margin: 4px 12px;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.notification-item:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.notification-item.unread {
  background: linear-gradient(135deg,
    rgba(102, 126, 234, 0.1),
    rgba(118, 75, 162, 0.05));
  border-color: rgba(102, 126, 234, 0.2);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
}

.notification-item.unread:hover {
  background: linear-gradient(135deg,
    rgba(102, 126, 234, 0.15),
    rgba(118, 75, 162, 0.1));
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

/* Priority Indicators */
.notification-item.priority-high {
  border-left: 4px solid #f59e0b;
}

.notification-item.priority-medium {
  border-left: 4px solid #3b82f6;
}

.notification-item.priority-low {
  border-left: 4px solid #10b981;
}

.notification-icon-container {
  flex-shrink: 0;
  position: relative;
}

/* Colorful Avatars */
.notification-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.notification-avatar.gold { background: linear-gradient(135deg, #f59e0b, #d97706); }
.notification-avatar.pink { background: linear-gradient(135deg, #ec4899, #db2777); }
.notification-avatar.blue { background: linear-gradient(135deg, #3b82f6, #2563eb); }
.notification-avatar.purple { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }
.notification-avatar.green { background: linear-gradient(135deg, #10b981, #059669); }
.notification-avatar.orange { background: linear-gradient(135deg, #f97316, #ea580c); }
.notification-avatar.yellow { background: linear-gradient(135deg, #eab308, #ca8a04); }

.notification-avatar::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  animation: avatarGlow 3s ease-in-out infinite;
}

@keyframes avatarGlow {
  0%, 100% { opacity: 0.3; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.1); }
}

/* Colorful Icon Wrappers */
.notification-icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.notification-icon-wrapper.gold { background: linear-gradient(135deg, #f59e0b, #d97706); }
.notification-icon-wrapper.pink { background: linear-gradient(135deg, #ec4899, #db2777); }
.notification-icon-wrapper.blue { background: linear-gradient(135deg, #3b82f6, #2563eb); }
.notification-icon-wrapper.purple { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }
.notification-icon-wrapper.green { background: linear-gradient(135deg, #10b981, #059669); }
.notification-icon-wrapper.orange { background: linear-gradient(135deg, #f97316, #ea580c); }
.notification-icon-wrapper.yellow { background: linear-gradient(135deg, #eab308, #ca8a04); }

.notification-icon {
  width: 20px;
  height: 20px;
  color: white;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
  z-index: 1;
  position: relative;
}

/* Priority Indicator */
.priority-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
}

.priority-indicator.high {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  animation: priorityPulse 2s ease-in-out infinite;
}

@keyframes priorityPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 6px;
}

.notification-title {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  line-height: 1.3;
}

.notification-time {
  font-size: 11px;
  color: #9ca3af;
  white-space: nowrap;
  margin-left: 8px;
  background: rgba(156, 163, 175, 0.1);
  padding: 2px 6px;
  border-radius: 8px;
}

.notification-message {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
  margin: 0 0 8px 0;
}

/* Notification Actions */
.notification-actions {
  display: flex;
  gap: 8px;
  margin-top: 8px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.notification-item:hover .notification-actions {
  opacity: 1;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.like-btn {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.like-btn:hover {
  background: rgba(16, 185, 129, 0.2);
  transform: scale(1.1);
}

.delete-btn {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.delete-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  transform: scale(1.1);
}

.action-icon {
  width: 14px;
  height: 14px;
}

.unread-indicator {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
  animation: unreadPulse 2s ease-in-out infinite;
}

@keyframes unreadPulse {
  0%, 100% {
    transform: translateY(-50%) scale(1);
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
  }
  50% {
    transform: translateY(-50%) scale(1.2);
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2);
  }
}

/* Enhanced No Notifications */
.no-notifications {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background: linear-gradient(135deg,
    rgba(102, 126, 234, 0.05),
    rgba(118, 75, 162, 0.02));
  margin: 12px;
  border-radius: 16px;
}

.no-notifications-icon {
  width: 64px;
  height: 64px;
  color: #d1d5db;
  margin-bottom: 16px;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.no-notifications p {
  font-size: 18px;
  font-weight: 600;
  color: #6b7280;
  margin: 0 0 8px 0;
}

.no-notifications span {
  font-size: 14px;
  color: #9ca3af;
  line-height: 1.4;
}

/* Enhanced Footer */
.notifications-footer {
  padding: 16px 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.8),
    rgba(255, 255, 255, 0.6));
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.footer-stats {
  font-size: 12px;
  color: #6b7280;
}

.stats-text {
  background: rgba(107, 114, 128, 0.1);
  padding: 4px 8px;
  border-radius: 8px;
}

.view-all-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
  color: white;
  font-size: 14px;
  font-weight: 600;
  padding: 10px 16px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.view-all-btn:hover {
  background: linear-gradient(135deg, #5a67d8, #6b46c1);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);
}

.view-all-icon {
  width: 16px;
  height: 16px;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .notifications-dropdown {
    right: 10px;
    left: 10px;
    width: auto;
    top: 60px;
    max-height: 80vh;
  }

  .notifications-header {
    padding: 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .notifications-actions {
    align-self: flex-end;
  }

  .notification-filters {
    padding: 12px 16px 0;
    gap: 6px;
    flex-wrap: wrap;
  }

  .filter-btn {
    padding: 6px 12px;
    font-size: 11px;
  }

  .notification-item {
    padding: 12px 16px;
    margin: 4px 8px;
    gap: 12px;
  }

  .notification-avatar,
  .notification-icon-wrapper {
    width: 36px;
    height: 36px;
  }

  .notification-icon {
    width: 18px;
    height: 18px;
  }

  .notification-title {
    font-size: 13px;
  }

  .notification-message {
    font-size: 12px;
  }

  .notifications-footer {
    padding: 12px 16px;
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .view-all-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .notifications-dropdown {
    right: 5px;
    left: 5px;
    top: 55px;
  }

  .notification-filters {
    padding: 8px 12px 0;
  }

  .filter-btn {
    flex: 1;
    text-align: center;
  }

  .notification-item {
    padding: 10px 12px;
    margin: 2px 4px;
  }
}
