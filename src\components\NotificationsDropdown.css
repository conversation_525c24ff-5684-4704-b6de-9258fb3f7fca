/* Notifications Dropdown */
.notifications-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: transparent;
  z-index: 1000;
}

.notifications-dropdown {
  position: absolute;
  top: 70px;
  right: 20px;
  width: 380px;
  max-height: 500px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

/* Header */
.notifications-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.notifications-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.notifications-title h3 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.unread-count {
  background-color: #3b82f6;
  color: white;
  font-size: 11px;
  font-weight: 500;
  padding: 2px 6px;
  border-radius: 10px;
}

.notifications-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mark-all-read-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  background: none;
  border: none;
  color: #3b82f6;
  font-size: 12px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.mark-all-read-btn:hover {
  background-color: #eff6ff;
}

.check-icon {
  width: 12px;
  height: 12px;
}

.close-notifications-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.close-notifications-btn:hover {
  background-color: #f3f4f6;
}

.close-icon {
  width: 16px;
  height: 16px;
  color: #6b7280;
}

/* Notifications List */
.notifications-list {
  max-height: 350px;
  overflow-y: auto;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px 20px;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
}

.notification-item:hover {
  background-color: #f9fafb;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item.unread {
  background-color: #eff6ff;
}

.notification-item.unread:hover {
  background-color: #dbeafe;
}

.notification-icon-container {
  flex-shrink: 0;
}

.notification-avatar {
  width: 32px;
  height: 32px;
  background-color: #3b82f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  font-weight: 600;
}

.notification-icon-wrapper {
  width: 32px;
  height: 32px;
  background-color: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-icon {
  width: 16px;
  height: 16px;
  color: #6b7280;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 4px;
}

.notification-title {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
  margin: 0;
  line-height: 1.3;
}

.notification-time {
  font-size: 11px;
  color: #6b7280;
  white-space: nowrap;
  margin-left: 8px;
}

.notification-message {
  font-size: 13px;
  color: #6b7280;
  line-height: 1.4;
  margin: 0;
}

.unread-indicator {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  background-color: #3b82f6;
  border-radius: 50%;
}

/* No Notifications */
.no-notifications {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.no-notifications-icon {
  width: 48px;
  height: 48px;
  color: #d1d5db;
  margin-bottom: 12px;
}

.no-notifications p {
  font-size: 16px;
  font-weight: 500;
  color: #6b7280;
  margin: 0 0 4px 0;
}

.no-notifications span {
  font-size: 14px;
  color: #9ca3af;
}

/* Footer */
.notifications-footer {
  padding: 12px 20px;
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.view-all-btn {
  width: 100%;
  background: none;
  border: none;
  color: #3b82f6;
  font-size: 14px;
  font-weight: 500;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.view-all-btn:hover {
  background-color: #eff6ff;
}

/* Responsive Design */
@media (max-width: 768px) {
  .notifications-dropdown {
    right: 10px;
    left: 10px;
    width: auto;
    top: 60px;
  }
  
  .notifications-header {
    padding: 12px 16px;
  }
  
  .notification-item {
    padding: 12px 16px;
  }
  
  .notifications-footer {
    padding: 8px 16px;
  }
}
