/* 📊 Enhanced MonthlyStats with Attractive Colors & Modern Design */
.monthly-stats-card {
  background: linear-gradient(145deg,
    rgba(255, 255, 255, 0.95),
    rgba(255, 255, 255, 0.85));
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
  position: relative;
}

.monthly-stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 30%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 70% 70%, rgba(168, 85, 247, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

/* Enhanced Header */
.monthly-stats-header {
  padding: 24px;
  background: linear-gradient(135deg,
    rgba(102, 126, 234, 0.1),
    rgba(168, 85, 247, 0.05));
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 1;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16px;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  width: 24px;
  height: 24px;
  color: #667eea;
  animation: iconFloat 3s ease-in-out infinite;
}

@keyframes iconFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-3px); }
}

.monthly-stats-title {
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

/* Period Selector */
.period-selector {
  display: flex;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  padding: 4px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.period-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  background: transparent;
  color: #6b7280;
}

.period-btn:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.period-btn.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Enhanced Content */
.monthly-stats-content {
  padding: 24px;
  position: relative;
  z-index: 1;
}

/* Enhanced Stat Items */
.stat-item {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.stat-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.stat-item.animate {
  animation: statItemSlideIn 0.6s ease-out forwards;
}

@keyframes statItemSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Color Variants */
.stat-item.blue { border-left: 4px solid #3b82f6; }
.stat-item.green { border-left: 4px solid #10b981; }
.stat-item.purple { border-left: 4px solid #8b5cf6; }
.stat-item.gold { border-left: 4px solid #f59e0b; }
.stat-item.orange { border-left: 4px solid #f97316; }
.stat-item.pink { border-left: 4px solid #ec4899; }

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.stat-item.blue::before { background: radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 70%); }
.stat-item.green::before { background: radial-gradient(circle at 50% 50%, rgba(16, 185, 129, 0.1) 0%, transparent 70%); }
.stat-item.purple::before { background: radial-gradient(circle at 50% 50%, rgba(139, 92, 246, 0.1) 0%, transparent 70%); }
.stat-item.gold::before { background: radial-gradient(circle at 50% 50%, rgba(245, 158, 11, 0.1) 0%, transparent 70%); }
.stat-item.orange::before { background: radial-gradient(circle at 50% 50%, rgba(249, 115, 22, 0.1) 0%, transparent 70%); }
.stat-item.pink::before { background: radial-gradient(circle at 50% 50%, rgba(236, 72, 153, 0.1) 0%, transparent 70%); }

.stat-item:hover::before {
  opacity: 1;
}

/* Stat Header */
.stat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.stat-icon-container {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.stat-item.blue .stat-icon-container { background: linear-gradient(135deg, #3b82f6, #2563eb); }
.stat-item.green .stat-icon-container { background: linear-gradient(135deg, #10b981, #059669); }
.stat-item.purple .stat-icon-container { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }
.stat-item.gold .stat-icon-container { background: linear-gradient(135deg, #f59e0b, #d97706); }
.stat-item.orange .stat-icon-container { background: linear-gradient(135deg, #f97316, #ea580c); }
.stat-item.pink .stat-icon-container { background: linear-gradient(135deg, #ec4899, #db2777); }

.stat-icon {
  width: 24px;
  height: 24px;
  color: white;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.trend-icon {
  width: 16px;
  height: 16px;
}

.trend-icon.up {
  color: #10b981;
  animation: trendUp 1s ease-in-out infinite;
}

.trend-icon.down {
  color: #ef4444;
  animation: trendDown 1s ease-in-out infinite;
}

@keyframes trendUp {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-2px); }
}

@keyframes trendDown {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(2px); }
}

/* Stat Main Content */
.stat-main {
  margin-bottom: 20px;
}

.stat-value-container {
  margin-bottom: 8px;
}

.stat-value {
  font-size: 32px;
  font-weight: 800;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 4px;
  display: block;
}

.stat-label {
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  display: block;
}

.stat-comparison {
  margin-top: 8px;
}

.previous-value {
  font-size: 12px;
  color: #9ca3af;
  background: rgba(156, 163, 175, 0.1);
  padding: 4px 8px;
  border-radius: 8px;
  display: inline-block;
}

/* Enhanced Progress Section */
.stat-progress-section {
  margin-bottom: 16px;
}

.progress-labels {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-label {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
}

.progress-value {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

.stat-progress-container {
  width: 100%;
  background: rgba(229, 231, 235, 0.5);
  border-radius: 12px;
  height: 8px;
  overflow: hidden;
  position: relative;
  margin-bottom: 12px;
}

.stat-progress-bar {
  height: 100%;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.stat-progress-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Progress Bar Colors */
.stat-progress-bar.blue { background: linear-gradient(135deg, #3b82f6, #2563eb); }
.stat-progress-bar.green { background: linear-gradient(135deg, #10b981, #059669); }
.stat-progress-bar.purple { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }
.stat-progress-bar.gold { background: linear-gradient(135deg, #f59e0b, #d97706); }
.stat-progress-bar.orange { background: linear-gradient(135deg, #f97316, #ea580c); }
.stat-progress-bar.pink { background: linear-gradient(135deg, #ec4899, #db2777); }

/* Target Section */
.target-section {
  margin-top: 12px;
}

.target-labels {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.target-label {
  font-size: 11px;
  font-weight: 500;
  color: #9ca3af;
}

.target-progress {
  font-size: 11px;
  font-weight: 600;
  color: #6b7280;
}

.target-progress-container {
  width: 100%;
  background: rgba(229, 231, 235, 0.3);
  border-radius: 8px;
  height: 4px;
  overflow: hidden;
}

.target-progress-bar {
  height: 100%;
  border-radius: 8px;
  opacity: 0.7;
}

.target-progress-bar.blue { background: linear-gradient(135deg, #3b82f6, #2563eb); }
.target-progress-bar.green { background: linear-gradient(135deg, #10b981, #059669); }
.target-progress-bar.purple { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }
.target-progress-bar.gold { background: linear-gradient(135deg, #f59e0b, #d97706); }
.target-progress-bar.orange { background: linear-gradient(135deg, #f97316, #ea580c); }
.target-progress-bar.pink { background: linear-gradient(135deg, #ec4899, #db2777); }

/* Achievements Section */
.achievements-section {
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.achievements-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 20px 0;
}

.achievements-icon {
  width: 24px;
  height: 24px;
  color: #f59e0b;
  animation: achievementGlow 2s ease-in-out infinite;
}

@keyframes achievementGlow {
  0%, 100% { filter: brightness(1); }
  50% { filter: brightness(1.2) drop-shadow(0 0 8px rgba(245, 158, 11, 0.5)); }
}

.achievements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 16px;
}

.achievement-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  border-radius: 16px;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
  text-align: center;
}

.achievement-item.unlocked {
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid rgba(16, 185, 129, 0.3);
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.1);
}

.achievement-item.locked {
  background: rgba(156, 163, 175, 0.1);
  border: 2px solid rgba(156, 163, 175, 0.2);
  opacity: 0.6;
}

.achievement-item:hover {
  transform: translateY(-2px);
}

.achievement-item.unlocked:hover {
  box-shadow: 0 8px 25px rgba(16, 185, 129, 0.2);
}

.achievement-icon {
  width: 32px;
  height: 32px;
  transition: all 0.3s ease;
}

.achievement-item.unlocked .achievement-icon {
  animation: achievementFloat 3s ease-in-out infinite;
}

@keyframes achievementFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-4px); }
}

.achievement-item.gold .achievement-icon { color: #f59e0b; }
.achievement-item.blue .achievement-icon { color: #3b82f6; }
.achievement-item.purple .achievement-icon { color: #8b5cf6; }
.achievement-item.green .achievement-icon { color: #10b981; }

.achievement-item.locked .achievement-icon {
  color: #9ca3af;
}

.achievement-name {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  line-height: 1.2;
}

.achievement-item.locked .achievement-name {
  color: #9ca3af;
}

.unlock-indicator {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 16px;
  height: 16px;
  color: #10b981;
  background: white;
  border-radius: 50%;
  padding: 2px;
  animation: unlockPulse 2s ease-in-out infinite;
}

@keyframes unlockPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
  }

  .stat-item {
    padding: 20px;
  }

  .achievements-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .monthly-stats-content {
    padding: 16px;
  }

  .monthly-stats-header {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .period-selector {
    width: 100%;
    justify-content: space-between;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .stat-item {
    padding: 16px;
  }

  .stat-value {
    font-size: 28px;
  }

  .achievements-section {
    padding: 16px;
  }

  .achievements-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .achievement-item {
    padding: 12px;
  }

  .achievement-icon {
    width: 28px;
    height: 28px;
  }

  .achievement-name {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .monthly-stats-content {
    padding: 12px;
  }

  .monthly-stats-header {
    padding: 12px;
  }

  .monthly-stats-title {
    font-size: 18px;
  }

  .period-btn {
    padding: 6px 12px;
    font-size: 11px;
  }

  .stat-item {
    padding: 12px;
  }

  .stat-icon-container {
    width: 40px;
    height: 40px;
  }

  .stat-icon {
    width: 20px;
    height: 20px;
  }

  .stat-value {
    font-size: 24px;
  }

  .achievements-grid {
    grid-template-columns: 1fr;
  }
}
