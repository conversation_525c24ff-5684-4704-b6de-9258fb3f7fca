/* Emoji Picker Styles */
.emoji-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
}

.emoji-picker-container {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  width: 320px;
  max-height: 400px;
  overflow: hidden;
}

.emoji-picker-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.emoji-picker-tabs {
  display: flex;
  gap: 8px;
}

.tab {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  background-color: transparent;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.tab:hover {
  background-color: #e5e7eb;
}

.tab.active {
  background-color: #3b82f6;
  color: white;
}

.close-picker {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #6b7280;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
}

.close-picker:hover {
  background-color: #e5e7eb;
}

.emoji-picker-content {
  padding: 16px;
  max-height: 300px;
  overflow-y: auto;
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 8px;
}

.emoji-item {
  background: none;
  border: none;
  font-size: 20px;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.emoji-item:hover {
  background-color: #f3f4f6;
}

.gif-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.gif-item {
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.2s ease;
  background-color: #f3f4f6;
}

.gif-item:hover {
  transform: scale(1.05);
}

.gif-item img {
  width: 100%;
  height: 80px;
  object-fit: cover;
  display: block;
}

.gif-title {
  display: block;
  padding: 8px;
  font-size: 12px;
  color: #6b7280;
  text-align: center;
}
