import { X, Award, Trophy, Users, TrendingUp, Star, Crown } from 'lucide-react'
import './RecipientDetailsModal.css'

const RecipientDetailsModal = ({ recipient, isOpen, onClose }) => {
  if (!isOpen || !recipient) return null

  const recentRecognitions = [
    {
      id: 1,
      from: '<PERSON>',
      category: 'Leadership',
      message: 'Outstanding leadership during the Q4 project. Your guidance helped the team exceed expectations.',
      date: '2 days ago',
      likes: 15,
      color: 'purple'
    },
    {
      id: 2,
      from: '<PERSON>',
      category: 'Teamwork',
      message: 'Always willing to help teammates and share knowledge. A true team player!',
      date: '1 week ago',
      likes: 12,
      color: 'blue'
    },
    {
      id: 3,
      from: '<PERSON>',
      category: 'Innovation',
      message: 'Brilliant solution to the customer portal issue. Your creativity saved us weeks of work.',
      date: '2 weeks ago',
      likes: 18,
      color: 'green'
    }
  ]

  const achievements = [
    {
      icon: Trophy,
      name: 'Top Performer',
      description: 'Highest recognition score this quarter',
      color: 'gold',
      gradient: 'from-yellow-400 to-orange-500'
    },
    {
      icon: Users,
      name: 'Team Player',
      description: 'Most collaborative team member',
      color: 'blue',
      gradient: 'from-blue-400 to-purple-500'
    },
    {
      icon: Star,
      name: 'Customer Champion',
      description: 'Outstanding customer service',
      color: 'pink',
      gradient: 'from-pink-400 to-red-500'
    },
    {
      icon: Crown,
      name: 'Innovation Leader',
      description: 'Most creative solutions',
      color: 'green',
      gradient: 'from-green-400 to-teal-500'
    }
  ]

  const monthlyStats = [
    { month: 'Jan', recognitions: 8 },
    { month: 'Feb', recognitions: 12 },
    { month: 'Mar', recognitions: 15 },
    { month: 'Apr', recognitions: 18 },
    { month: 'May', recognitions: 22 },
    { month: 'Jun', recognitions: 25 }
  ]

  return (
    <div className="recipient-modal-overlay" onClick={onClose}>
      <div className="recipient-modal-container" onClick={(e) => e.stopPropagation()}>
        {/* Enhanced Header with Gradient Background */}
        <div className="recipient-modal-header">
          <div className="header-background-pattern"></div>
          <div className="recipient-modal-profile">
            <div className="recipient-modal-avatar-wrapper">
              <div className="recipient-modal-avatar">
                <span className="recipient-modal-avatar-text">
                  {recipient.name.split(' ').map(n => n[0]).join('')}
                </span>
                <div className="avatar-glow"></div>
              </div>
              <div className="status-indicator"></div>
            </div>
            <div className="recipient-modal-info">
              <h2 className="recipient-modal-name">{recipient.name}</h2>
              <p className="recipient-modal-title">
                <Star className="title-icon" />
                Senior Software Engineer
              </p>
              <p className="recipient-modal-department">
                <Users className="department-icon" />
                Engineering Team
              </p>
              <div className="profile-badges">
                <span className="profile-badge verified">✓ Verified</span>
                <span className="profile-badge top-performer">🏆 Top 5%</span>
              </div>
            </div>
          </div>
          <button onClick={onClose} className="recipient-modal-close">
            <X className="close-icon" />
          </button>
        </div>

        {/* Enhanced Stats Overview */}
        <div className="recipient-modal-stats">
          <div className="stat-item points">
            <div className="stat-icon-wrapper">
              <Trophy className="stat-icon" />
            </div>
            <div className="stat-content">
              <div className="stat-number">{recipient.points}</div>
              <div className="stat-label">Total Points</div>
            </div>
            <div className="stat-sparkle">✨</div>
          </div>
          <div className="stat-item badges">
            <div className="stat-icon-wrapper">
              <Award className="stat-icon" />
            </div>
            <div className="stat-content">
              <div className="stat-number">{recipient.badges}</div>
              <div className="stat-label">Badges Earned</div>
            </div>
            <div className="stat-sparkle">🏅</div>
          </div>
          <div className="stat-item rank">
            <div className="stat-icon-wrapper">
              <Crown className="stat-icon" />
            </div>
            <div className="stat-content">
              <div className="stat-number">#{recipient.rank}</div>
              <div className="stat-label">Current Rank</div>
            </div>
            <div className="stat-sparkle">👑</div>
          </div>
          <div className="stat-item monthly">
            <div className="stat-icon-wrapper">
              <TrendingUp className="stat-icon" />
            </div>
            <div className="stat-content">
              <div className="stat-number">25</div>
              <div className="stat-label">This Month</div>
            </div>
            <div className="stat-sparkle">🚀</div>
          </div>
        </div>

        {/* Content Sections */}
        <div className="recipient-modal-content">
          {/* Enhanced Achievements */}
          <div className="modal-section">
            <h3 className="section-title">
              <Trophy className="section-icon" />
              Achievements & Badges
              <span className="section-count">{achievements.length}</span>
            </h3>
            <div className="achievements-grid">
              {achievements.map((achievement, index) => (
                <div key={index} className={`achievement-card ${achievement.color}`}>
                  <div className="achievement-card-header">
                    <div className="achievement-icon-wrapper">
                      <achievement.icon className="achievement-card-icon" />
                      <div className="achievement-glow"></div>
                    </div>
                    <div className="achievement-level">Level {index + 1}</div>
                  </div>
                  <div className="achievement-card-info">
                    <h4 className="achievement-card-name">{achievement.name}</h4>
                    <p className="achievement-card-desc">{achievement.description}</p>
                  </div>
                  <div className="achievement-progress">
                    <div className="progress-bar">
                      <div className="progress-fill" style={{ width: '100%' }}></div>
                    </div>
                    <span className="progress-text">Completed</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Enhanced Recent Recognitions */}
          <div className="modal-section">
            <h3 className="section-title">
              <Award className="section-icon" />
              Recent Recognitions
              <span className="section-count">{recentRecognitions.length}</span>
            </h3>
            <div className="recognitions-list">
              {recentRecognitions.map((recognition) => (
                <div key={recognition.id} className={`recognition-card ${recognition.color}`}>
                  <div className="recognition-border-glow"></div>
                  <div className="recognition-header">
                    <div className="recognition-from">
                      <div className="recognition-avatar-wrapper">
                        <div className="recognition-avatar">
                          {recognition.from.split(' ').map(n => n[0]).join('')}
                        </div>
                        <div className="avatar-status-dot"></div>
                      </div>
                      <div className="recognition-meta">
                        <span className="recognition-author">{recognition.from}</span>
                        <span className={`recognition-category ${recognition.color}`}>
                          {recognition.category}
                        </span>
                      </div>
                    </div>
                    <div className="recognition-time">
                      <span className="recognition-date">{recognition.date}</span>
                      <div className="recognition-points">+{10 + recognition.id * 2} pts</div>
                    </div>
                  </div>
                  <p className="recognition-message">{recognition.message}</p>
                  <div className="recognition-footer">
                    <div className="recognition-reactions">
                      <span className="recognition-likes">❤️ {recognition.likes}</span>
                      <span className="recognition-reaction">👏 {Math.floor(recognition.likes * 0.8)}</span>
                      <span className="recognition-reaction">🎉 {Math.floor(recognition.likes * 0.6)}</span>
                    </div>
                    <button className="recognition-reply-btn">Reply</button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Enhanced Performance Trend */}
          <div className="modal-section">
            <h3 className="section-title">
              <TrendingUp className="section-icon" />
              Recognition Trend
              <span className="trend-indicator">📈 +15% this month</span>
            </h3>
            <div className="trend-chart-container">
              <div className="trend-chart">
                {monthlyStats.map((stat, index) => (
                  <div key={index} className="trend-bar">
                    <div className="trend-bar-wrapper">
                      <div
                        className="trend-bar-fill"
                        style={{ height: `${(stat.recognitions / 25) * 100}%` }}
                      >
                        <div className="bar-glow"></div>
                      </div>
                    </div>
                    <span className="trend-month">{stat.month}</span>
                    <span className="trend-value">{stat.recognitions}</span>
                  </div>
                ))}
              </div>
              <div className="trend-summary">
                <div className="trend-stat">
                  <span className="trend-stat-label">Average</span>
                  <span className="trend-stat-value">17.5</span>
                </div>
                <div className="trend-stat">
                  <span className="trend-stat-label">Peak</span>
                  <span className="trend-stat-value">25</span>
                </div>
                <div className="trend-stat">
                  <span className="trend-stat-label">Growth</span>
                  <span className="trend-stat-value">+212%</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default RecipientDetailsModal
