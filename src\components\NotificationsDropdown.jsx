import { useState, useEffect } from 'react'
import { X, Heart, MessageCircle, Award, Users, CheckCircle, Bell, Star, Trophy, Gift, Zap, ThumbsUp } from 'lucide-react'
import './NotificationsDropdown.css'

const NotificationsDropdown = ({ isOpen, onClose, notificationCount, onMarkAllRead, onViewAll }) => {
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      type: 'recognition',
      icon: Award,
      title: 'New Recognition Received! 🎉',
      message: '<PERSON> recognized you for "Outstanding Leadership" with 50 points',
      time: '5 minutes ago',
      isRead: false,
      avatar: 'SC',
      color: 'gold',
      priority: 'high'
    },
    {
      id: 2,
      type: 'like',
      icon: Heart,
      title: 'Your post is getting love! ❤️',
      message: '<PERSON> and 3 others liked your recognition post',
      time: '1 hour ago',
      isRead: false,
      avatar: 'MT',
      color: 'pink',
      priority: 'medium'
    },
    {
      id: 3,
      type: 'comment',
      icon: MessageCircle,
      title: 'New comment on your post 💬',
      message: '<PERSON> commented: "Well deserved! Keep up the great work!"',
      time: '2 hours ago',
      isRead: false,
      avatar: 'EJ',
      color: 'blue',
      priority: 'medium'
    },
    {
      id: 4,
      type: 'achievement',
      icon: Trophy,
      title: 'Achievement Unlocked! 🏆',
      message: 'You earned the "Team Player" badge and 100 bonus points!',
      time: '1 day ago',
      isRead: true,
      avatar: null,
      color: 'purple',
      priority: 'high'
    },
    {
      id: 5,
      type: 'mention',
      icon: Users,
      title: 'You were mentioned! 👥',
      message: 'David Kim mentioned you in a recognition post about teamwork',
      time: '2 days ago',
      isRead: true,
      avatar: 'DK',
      color: 'green',
      priority: 'low'
    },
    {
      id: 6,
      type: 'milestone',
      icon: Star,
      title: 'Milestone Reached! ⭐',
      message: 'Congratulations! You\'ve received 10 recognitions this month',
      time: '3 days ago',
      isRead: true,
      avatar: null,
      color: 'orange',
      priority: 'high'
    },
    {
      id: 7,
      type: 'points',
      icon: Zap,
      title: 'Points Earned! ⚡',
      message: 'You earned 25 points for completing your monthly goals',
      time: '1 week ago',
      isRead: true,
      avatar: null,
      color: 'yellow',
      priority: 'low'
    }
  ])

  const [filter, setFilter] = useState('all') // all, unread, recognition, achievement

  const markAsRead = (id) => {
    setNotifications(prev => prev.map(notification =>
      notification.id === id
        ? { ...notification, isRead: true }
        : notification
    ))
  }

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(notification => ({ ...notification, isRead: true })))
    onMarkAllRead && onMarkAllRead()
  }

  const deleteNotification = (id) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id))
  }

  const getFilteredNotifications = () => {
    switch (filter) {
      case 'unread':
        return notifications.filter(n => !n.isRead)
      case 'recognition':
        return notifications.filter(n => n.type === 'recognition' || n.type === 'achievement')
      case 'social':
        return notifications.filter(n => n.type === 'like' || n.type === 'comment' || n.type === 'mention')
      default:
        return notifications
    }
  }

  const filteredNotifications = getFilteredNotifications()
  const unreadCount = notifications.filter(n => !n.isRead).length

  const getNotificationTypeColor = (type, color) => {
    const colorMap = {
      gold: '#f59e0b',
      pink: '#ec4899',
      blue: '#3b82f6',
      purple: '#8b5cf6',
      green: '#10b981',
      orange: '#f97316',
      yellow: '#eab308'
    }
    return colorMap[color] || '#6b7280'
  }

  if (!isOpen) return null

  return (
    <div className="notifications-overlay" onClick={onClose}>
      <div className="notifications-dropdown" onClick={(e) => e.stopPropagation()}>
        {/* Header */}
        <div className="notifications-header">
          <div className="notifications-title">
            <div className="title-with-icon">
              <Bell className="bell-icon" />
              <h3>Notifications</h3>
            </div>
            {unreadCount > 0 && (
              <span className="unread-count">{unreadCount} new</span>
            )}
          </div>
          <div className="notifications-actions">
            {unreadCount > 0 && (
              <button onClick={markAllAsRead} className="mark-all-read-btn">
                <CheckCircle className="check-icon" />
                Mark all read
              </button>
            )}
            <button onClick={onClose} className="close-notifications-btn">
              <X className="close-icon" />
            </button>
          </div>
        </div>

        {/* Filter Tabs */}
        <div className="notification-filters">
          <button
            className={`filter-btn ${filter === 'all' ? 'active' : ''}`}
            onClick={() => setFilter('all')}
          >
            All
          </button>
          <button
            className={`filter-btn ${filter === 'unread' ? 'active' : ''}`}
            onClick={() => setFilter('unread')}
          >
            Unread ({unreadCount})
          </button>
          <button
            className={`filter-btn ${filter === 'recognition' ? 'active' : ''}`}
            onClick={() => setFilter('recognition')}
          >
            Recognition
          </button>
          <button
            className={`filter-btn ${filter === 'social' ? 'active' : ''}`}
            onClick={() => setFilter('social')}
          >
            Social
          </button>
        </div>

        {/* Notifications List */}
        <div className="notifications-list">
          {filteredNotifications.length === 0 ? (
            <div className="no-notifications">
              <Bell className="no-notifications-icon" />
              <p>No notifications found</p>
              <span>
                {filter === 'unread'
                  ? "You're all caught up!"
                  : filter === 'all'
                    ? "You'll see updates here"
                    : `No ${filter} notifications`
                }
              </span>
            </div>
          ) : (
            filteredNotifications.map((notification) => (
              <div
                key={notification.id}
                className={`notification-item ${!notification.isRead ? 'unread' : ''} ${notification.color} priority-${notification.priority}`}
                onClick={() => markAsRead(notification.id)}
              >
                <div className="notification-icon-container">
                  {notification.avatar ? (
                    <div className={`notification-avatar ${notification.color}`}>
                      {notification.avatar}
                    </div>
                  ) : (
                    <div className={`notification-icon-wrapper ${notification.color}`}>
                      <notification.icon className="notification-icon" />
                    </div>
                  )}
                  {notification.priority === 'high' && (
                    <div className="priority-indicator high"></div>
                  )}
                </div>

                <div className="notification-content">
                  <div className="notification-header">
                    <h4 className="notification-title">{notification.title}</h4>
                    <span className="notification-time">{notification.time}</span>
                  </div>
                  <p className="notification-message">{notification.message}</p>
                  <div className="notification-actions">
                    <button
                      className="action-btn like-btn"
                      onClick={(e) => {
                        e.stopPropagation()
                        // Handle like action
                      }}
                    >
                      <ThumbsUp className="action-icon" />
                    </button>
                    <button
                      className="action-btn delete-btn"
                      onClick={(e) => {
                        e.stopPropagation()
                        deleteNotification(notification.id)
                      }}
                    >
                      <X className="action-icon" />
                    </button>
                  </div>
                </div>

                {!notification.isRead && (
                  <div className="unread-indicator"></div>
                )}
              </div>
            ))
          )}
        </div>

        {/* Footer */}
        {notifications.length > 0 && (
          <div className="notifications-footer">
            <div className="footer-stats">
              <span className="stats-text">
                {filteredNotifications.length} of {notifications.length} notifications
              </span>
            </div>
            <button onClick={onViewAll} className="view-all-btn">
              <Award className="view-all-icon" />
              View all notifications
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

export default NotificationsDropdown
