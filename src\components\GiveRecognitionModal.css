/* 🌟 HRMS Recognition Modal - Ultra Modern & Attractive Design 🌟 */

/* Enhanced Modal Overlay with Dynamic Gradient */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(99, 102, 241, 0.9),
    rgba(168, 85, 247, 0.9),
    rgba(236, 72, 153, 0.9));
  backdrop-filter: blur(15px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: fadeInOverlay 0.5s ease-out;
}

@keyframes fadeInOverlay {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
    transform: scale(1.1);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(15px);
    transform: scale(1);
  }
}

/* Floating particles animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(120deg);
  }
  66% {
    transform: translateY(5px) rotate(240deg);
  }
}

/* Enhanced Modal Container with Glass Morphism - Compact Size */
.modal-container {
  background: linear-gradient(145deg,
    rgba(255, 255, 255, 0.95),
    rgba(255, 255, 255, 0.85));
  backdrop-filter: blur(25px);
  border-radius: 20px;
  box-shadow:
    0 20px 40px -8px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  max-width: 480px;
  width: 100%;
  max-height: 85vh;
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideUpScale 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
}

/* Floating background elements */
.modal-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(168, 85, 247, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(236, 72, 153, 0.1) 0%, transparent 50%);
  border-radius: 28px;
  pointer-events: none;
  animation: float 6s ease-in-out infinite;
}

@keyframes slideUpScale {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.9) rotateX(10deg);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1) rotateX(0deg);
  }
}

/* Enhanced Header with Stunning Gradient - Compact */
.modal-header {
  background: linear-gradient(135deg,
    #667eea 0%,
    #764ba2 50%,
    #f093fb 100%);
  padding: 24px 20px;
  border-radius: 20px 20px 0 0;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Animated sparkle pattern */
.modal-header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="sparkles" x="0" y="0" width="25" height="25" patternUnits="userSpaceOnUse"><circle cx="5" cy="5" r="1" fill="rgba(255,255,255,0.4)"/><circle cx="20" cy="15" r="0.5" fill="rgba(255,255,255,0.6)"/><circle cx="15" cy="20" r="0.8" fill="rgba(255,255,255,0.3)"/></pattern></defs><rect width="100" height="100" fill="url(%23sparkles)"/></svg>');
  opacity: 0.4;
  animation: sparkleMove 20s linear infinite;
}

@keyframes sparkleMove {
  0% { transform: translateX(0) translateY(0); }
  100% { transform: translateX(-25px) translateY(-25px); }
}

.modal-header h2 {
  font-size: 24px;
  font-weight: 800;
  color: white;
  margin: 0;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  position: relative;
  z-index: 1;
  animation: titleGlow 3s ease-in-out infinite;
}

@keyframes titleGlow {
  0%, 100% {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }
  50% {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255, 255, 255, 0.5);
  }
}

.modal-subtitle {
  color: rgba(255, 255, 255, 0.95);
  font-size: 18px;
  font-weight: 500;
  margin-top: 12px;
  position: relative;
  z-index: 1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.close-button {
  background: rgba(255, 255, 255, 0.25);
  border: none;
  cursor: pointer;
  padding: 16px;
  border-radius: 50%;
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.4);
  transform: rotate(90deg) scale(1.15);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.close-icon {
  width: 28px;
  height: 28px;
  color: white;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.modal-form {
  padding: 16px;
}

.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.required {
  color: #379cba;
}

/* Search Container */
.search-container {
  position: relative;
  margin-bottom: 12px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  color: #9ca3af;
}

.search-input {
  width: 100%;
  padding: 10px 12px 10px 40px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #08ccf3;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Recipients List - Compact */
.recipients-list {
  max-height: 120px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background-color: #f9fafb;
}

.recipient-item {
  display: flex;
  align-items: center;
  padding: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #e5e7eb;
}

.recipient-item:last-child {
  border-bottom: none;
}

.recipient-item:hover {
  background-color: #f3f4f6;
}

.recipient-item.selected {
  background-color: #eff6ff;
  border-color: #3b82f6;
}

.recipient-avatar {
  width: 32px;
  height: 32px;
  background-color: #d17723;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 12px;
  margin-right: 12px;
}

.recipient-info {
  display: flex;
  flex-direction: column;
}

.recipient-name {
  font-size: 14px;
  font-weight: 500;
  color: #111827;
}

.recipient-department {
  font-size: 12px;
  color: #6b7280;
}

/* Selected Recipients */
.selected-recipients {
  margin-top: 12px;
  padding: 12px;
  background-color: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.selected-label {
  font-size: 12px;
  color: #6b7280;
  margin-right: 8px;
}

.selected-tag {
  display: inline-flex;
  align-items: center;
  background-color: #3b82f6;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-right: 8px;
  margin-bottom: 4px;
}

.remove-tag {
  background: none;
  border: none;
  color: white;
  margin-left: 4px;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
}

/* Category Grid */
.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.category-item:hover {
  border-color: #4b75b1;
  background-color: #f8fafc;
}

.category-item.selected {
  border-color: #69b7d9;
  background-color: #eff6ff;
  color: #56a5c5;
}

.category-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

/* Category Select Dropdown */
.category-select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  color: #374151;
  cursor: pointer;
  transition: border-color 0.2s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  background-size: 16px;
  padding-right: 40px;
}

.category-select:focus {
  outline: none;
  border-color: #5fc9ed;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Message Textarea - Compact */
.message-textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  min-height: 80px;
  transition: border-color 0.2s ease;
}

.message-textarea:focus {
  outline: none;
  border-color: #5fc9ed;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Optional Features */
.optional-features {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.feature-button {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background-color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.feature-button:hover {
  border-color: #3f97ab;
  background-color: #f8fafc;
}

.feature-icon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

/* Attachments List */
.attachments-list {
  margin-top: 12px;
  padding: 12px;
  background-color: #f9fafb;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
}

.attachment-item {
  display: inline-block;
  background-color: #e5e7eb;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  margin-right: 8px;
  margin-bottom: 4px;
}

/* Visibility Options */
.visibility-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.radio-option {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.radio-option input[type="radio"] {
  margin-right: 8px;
  accent-color: #3b82f6;
}

.radio-label {
  font-size: 14px;
  color: #374151;
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.cancel-button {
  padding: 10px 20px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background-color: white;
  color: #374151;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.cancel-button:hover {
  background-color: #f9fafb;
}

.submit-button {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  color: white;
  cursor: pointer;
  font-size: 16px;
  font-weight: 700;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
  position: relative;
  overflow: hidden;
}

.submit-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.submit-button:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 50%, #ec4899 100%);
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
}

.submit-button:hover::before {
  left: 100%;
}

.submit-button:active {
  transform: translateY(0) scale(1.02);
}

/* Enhanced Success Message with Celebrations */
.success-message {
  padding: 60px 32px;
  text-align: center;
  background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
  border-radius: 28px;
  position: relative;
  overflow: hidden;
  animation: successBounce 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes successBounce {
  0% {
    opacity: 0;
    transform: scale(0.3) rotate(-10deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.1) rotate(5deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

/* Confetti animation */
.success-message::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="confetti" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="5" cy="5" r="1" fill="rgba(255,255,255,0.6)"/><rect x="15" y="10" width="2" height="2" fill="rgba(255,255,255,0.4)"/><polygon points="10,15 12,17 8,17" fill="rgba(255,255,255,0.5)"/></pattern></defs><rect width="100" height="100" fill="url(%23confetti)"/></svg>');
  opacity: 0.3;
  animation: confettiFall 3s ease-in-out infinite;
}

@keyframes confettiFall {
  0% { transform: translateY(-20px) rotate(0deg); }
  100% { transform: translateY(20px) rotate(360deg); }
}

.success-icon {
  font-size: 64px;
  margin-bottom: 24px;
  animation: iconPulse 2s ease-in-out infinite;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

@keyframes iconPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.success-message h3 {
  font-size: 28px;
  font-weight: 800;
  color: white;
  margin: 0 0 16px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  animation: textGlow 2s ease-in-out infinite;
}

@keyframes textGlow {
  0%, 100% {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }
  50% {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255, 255, 255, 0.5);
  }
}

.success-message p {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 10px;
  }

  .modal-container {
    max-height: 95vh;
  }

  .modal-header,
  .modal-form {
    padding: 16px;
  }

  .category-grid {
    grid-template-columns: 1fr;
  }

  .optional-features {
    flex-direction: column;
  }

  .form-actions {
    flex-direction: column-reverse;
  }

  .cancel-button,
  .submit-button {
    width: 100%;
  }
}
 /* Add these styles to your GiveRecognitionModal.css file */

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.visibility-select-container {
  min-width: 140px;
}

.visibility-select {
  width: 100%;
  padding: 6px 8px;
  font-size: 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  background-color: #f9fafb;
  color: #374151;
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='%236b7280'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 9l-7 7-7-7'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 16px;
  padding-right: 28px;
}

.visibility-select:focus {
  outline: none;
  border-color: #4284bd;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

@media (max-width: 640px) {
  .form-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .visibility-select-container {
    width: 100%;
  }
}

/* ✨ Enhanced Interactive Effects ✨ */

/* Shimmer loading effect */
.shimmer-effect {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Glow effect for interactive elements */
.glow-on-hover {
  transition: all 0.3s ease;
}

.glow-on-hover:hover {
  box-shadow: 0 0 20px rgba(139, 92, 246, 0.4);
  transform: translateY(-2px);
}

/* Pulse animation for required fields */
.pulse-required {
  animation: pulseGlow 2s ease-in-out infinite;
}

@keyframes pulseGlow {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

/* Enhanced loading spinner */
.loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #ffffff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Card hover effects */
.card-interactive {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.card-interactive:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Gradient text effect */
.gradient-text-effect {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Accessibility enhancements */
.focus-visible {
  outline: 3px solid #667eea;
  outline-offset: 2px;
  border-radius: 4px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .modal-container {
    border: 3px solid #000000;
    background: #ffffff;
  }

  .modal-header {
    background: #000000;
    color: #ffffff;
  }
}

/* Reduced motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .modal-container {
    background: linear-gradient(145deg,
      rgba(31, 41, 55, 0.95),
      rgba(17, 24, 39, 0.9));
    color: #ffffff;
  }

  .modal-form {
    background: rgba(31, 41, 55, 0.8);
  }
}