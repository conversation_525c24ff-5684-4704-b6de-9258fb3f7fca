import { useState, useEffect } from 'react'
import { TrendingUp, TrendingDown, Award, Heart, Users, Target, Calendar, Trophy, Star, Zap, Gift, Crown } from 'lucide-react'
import './MonthlyStats.css'

const MonthlyStats = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('month')
  const [animateProgress, setAnimateProgress] = useState(false)

  const currentMonth = new Date().toLocaleString('default', { month: 'long' })

  const stats = [
    {
      id: 'sent',
      label: 'Recognition Sent',
      value: 24,
      previousValue: 18,
      maxValue: 50,
      target: 30,
      color: 'blue',
      icon: Award,
      trend: 'up',
      percentage: 33,
      description: 'Great job spreading positivity!'
    },
    {
      id: 'received',
      label: 'Recognition Received',
      value: 18,
      previousValue: 15,
      maxValue: 50,
      target: 25,
      color: 'green',
      icon: Heart,
      trend: 'up',
      percentage: 20,
      description: 'Your work is being noticed!'
    },
    {
      id: 'points',
      label: 'Points Earned',
      value: 1250,
      previousValue: 980,
      maxValue: 2000,
      target: 1500,
      color: 'purple',
      icon: Zap,
      trend: 'up',
      percentage: 28,
      description: 'Amazing point accumulation!'
    },
    {
      id: 'rank',
      label: 'Current Rank',
      value: 5,
      previousValue: 8,
      maxValue: 100,
      target: 3,
      color: 'gold',
      icon: Crown,
      trend: 'up',
      percentage: 37,
      description: 'Climbing the leaderboard!'
    },
    {
      id: 'streak',
      label: 'Recognition Streak',
      value: 7,
      previousValue: 4,
      maxValue: 30,
      target: 10,
      color: 'orange',
      icon: Target,
      trend: 'up',
      percentage: 75,
      description: 'Keep the momentum going!'
    },
    {
      id: 'team',
      label: 'Team Recognitions',
      value: 42,
      previousValue: 35,
      maxValue: 100,
      target: 60,
      color: 'pink',
      icon: Users,
      trend: 'up',
      percentage: 20,
      description: 'Team spirit is strong!'
    }
  ]

  const achievements = [
    { name: 'Recognition Master', icon: Trophy, color: 'gold', unlocked: true },
    { name: 'Team Player', icon: Users, color: 'blue', unlocked: true },
    { name: 'Streak Champion', icon: Target, color: 'purple', unlocked: false },
    { name: 'Point Collector', icon: Star, color: 'green', unlocked: true }
  ]

  // Animate progress bars on mount
  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimateProgress(true)
    }, 300)
    return () => clearTimeout(timer)
  }, [])

  const getTrendIcon = (trend) => {
    return trend === 'up' ? TrendingUp : TrendingDown
  }

  const formatValue = (value, id) => {
    if (id === 'points') {
      return value.toLocaleString()
    }
    if (id === 'rank') {
      return `#${value}`
    }
    return value
  }

  const getProgressPercentage = (value, maxValue) => {
    return Math.min((value / maxValue) * 100, 100)
  }

  const getTargetProgress = (value, target) => {
    return Math.min((value / target) * 100, 100)
  }

  return (
    <div className="monthly-stats-card">
      {/* Enhanced Header */}
      <div className="monthly-stats-header">
        <div className="header-content">
          <div className="header-title">
            <Calendar className="header-icon" />
            <h2 className="monthly-stats-title">{currentMonth} Statistics</h2>
          </div>
          <div className="period-selector">
            <button
              className={`period-btn ${selectedPeriod === 'week' ? 'active' : ''}`}
              onClick={() => setSelectedPeriod('week')}
            >
              Week
            </button>
            <button
              className={`period-btn ${selectedPeriod === 'month' ? 'active' : ''}`}
              onClick={() => setSelectedPeriod('month')}
            >
              Month
            </button>
            <button
              className={`period-btn ${selectedPeriod === 'year' ? 'active' : ''}`}
              onClick={() => setSelectedPeriod('year')}
            >
              Year
            </button>
          </div>
        </div>
      </div>

      {/* Enhanced Stats Grid */}
      <div className="monthly-stats-content">
        <div className="stats-grid">
          {stats.map((stat, index) => {
            const IconComponent = stat.icon
            const TrendIcon = getTrendIcon(stat.trend)
            const progressPercentage = getProgressPercentage(stat.value, stat.maxValue)
            const targetProgress = getTargetProgress(stat.value, stat.target)

            return (
              <div key={stat.id} className={`stat-item ${stat.color} ${animateProgress ? 'animate' : ''}`}>
                <div className="stat-header">
                  <div className="stat-icon-container">
                    <IconComponent className="stat-icon" />
                  </div>
                  <div className="stat-trend">
                    <TrendIcon className={`trend-icon ${stat.trend}`} />
                    <span className="trend-percentage">+{stat.percentage}%</span>
                  </div>
                </div>

                <div className="stat-main">
                  <div className="stat-value-container">
                    <span className="stat-value">{formatValue(stat.value, stat.id)}</span>
                    <span className="stat-label">{stat.label}</span>
                  </div>

                  <div className="stat-comparison">
                    <span className="previous-value">
                      vs {formatValue(stat.previousValue, stat.id)} last period
                    </span>
                  </div>
                </div>

                {/* Enhanced Progress Bar */}
                <div className="stat-progress-section">
                  <div className="progress-labels">
                    <span className="progress-label">Progress</span>
                    <span className="progress-value">{Math.round(progressPercentage)}%</span>
                  </div>
                  <div className="stat-progress-container">
                    <div
                      className={`stat-progress-bar ${stat.color}`}
                      style={{
                        width: animateProgress ? `${progressPercentage}%` : '0%',
                        transition: 'width 1s ease-out'
                      }}
                    ></div>
                  </div>

                  {/* Target Indicator */}
                  <div className="target-section">
                    <div className="target-labels">
                      <span className="target-label">Target: {formatValue(stat.target, stat.id)}</span>
                      <span className="target-progress">{Math.round(targetProgress)}%</span>
                    </div>
                    <div className="target-progress-container">
                      <div
                        className={`target-progress-bar ${stat.color}`}
                        style={{
                          width: animateProgress ? `${targetProgress}%` : '0%',
                          transition: 'width 1.2s ease-out'
                        }}
                      ></div>
                    </div>
                  </div>
                </div>

                <div className="stat-description">
                  {stat.description}
                </div>
              </div>
            )
          })}
        </div>

        {/* Achievements Section */}
        <div className="achievements-section">
          <h3 className="achievements-title">
            <Trophy className="achievements-icon" />
            Recent Achievements
          </h3>
          <div className="achievements-grid">
            {achievements.map((achievement, index) => {
              const AchievementIcon = achievement.icon
              return (
                <div
                  key={index}
                  className={`achievement-item ${achievement.color} ${achievement.unlocked ? 'unlocked' : 'locked'}`}
                >
                  <AchievementIcon className="achievement-icon" />
                  <span className="achievement-name">{achievement.name}</span>
                  {achievement.unlocked && <Gift className="unlock-indicator" />}
                </div>
              )
            })}
          </div>
        </div>
      </div>
    </div>
  )
}

export default MonthlyStats
