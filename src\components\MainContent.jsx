import { useState, useEffect } from 'react'
import { Plus, Bell, Star, Trophy, Users, TrendingUp, Gift, Sparkles, Award, Heart, Zap, Target } from 'lucide-react'
import RecognitionFeed from './RecognitionFeed'
import TopRecipients from './TopRecipients'
import MonthlyStats from './MonthlyStats'
import GiveRecognitionModal from './GiveRecognitionModal'
import NotificationsDropdown from './NotificationsDropdown'
import NotificationsPage from './NotificationsPage'
import './MainContent.css'

const MainContent = () => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false)
  const [isNotificationsPageOpen, setIsNotificationsPageOpen] = useState(false)
  const [notificationCount, setNotificationCount] = useState(3)
  const [currentTime, setCurrentTime] = useState(new Date())
  const [userStats, setUserStats] = useState({
    totalRecognitions: 24,
    pointsEarned: 1250,
    rank: 5,
    streak: 7
  })

  // Update time every minute
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 60000)
    return () => clearInterval(timer)
  }, [])

  const handleOpenModal = () => {
    console.log('Opening Give Recognition Modal')
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
    // Simulate updating stats after giving recognition
    setUserStats(prev => ({
      ...prev,
      totalRecognitions: prev.totalRecognitions + 1,
      pointsEarned: prev.pointsEarned + 50
    }))
  }

  const handleToggleNotifications = () => {
    setIsNotificationsOpen(!isNotificationsOpen)
  }

  const handleCloseNotifications = () => {
    setIsNotificationsOpen(false)
  }

  const handleMarkAllRead = () => {
    setNotificationCount(0)
  }

  const handleViewAllNotifications = () => {
    setIsNotificationsOpen(false)
    setIsNotificationsPageOpen(true)
  }

  const handleCloseNotificationsPage = () => {
    setIsNotificationsPageOpen(false)
  }

  const getGreeting = () => {
    const hour = currentTime.getHours()
    if (hour < 12) return 'Good Morning'
    if (hour < 17) return 'Good Afternoon'
    return 'Good Evening'
  }

  const getMotivationalMessage = () => {
    const messages = [
      "Ready to make someone's day brighter?",
      "Spread positivity and recognition today!",
      "Your appreciation can inspire greatness!",
      "Let's celebrate achievements together!",
      "Recognition is the fuel of excellence!"
    ]
    return messages[Math.floor(Math.random() * messages.length)]
  }

  return (
    <div className="main-content">
      <div className="main-container">
        {/* Enhanced Header with Greeting */}
        <div className="main-header">
          <div className="header-content">
            <div className="greeting-section">
              <h1 className="main-title">
                <Sparkles className="title-icon" />
                {getGreeting()}! 👋
              </h1>
              <p className="main-subtitle">{getMotivationalMessage()}</p>
            </div>
            <div className="user-stats-preview">
              <div className="stat-item">
                <Trophy className="stat-icon gold" />
                <span className="stat-value">{userStats.totalRecognitions}</span>
                <span className="stat-label">Given</span>
              </div>
              <div className="stat-item">
                <Zap className="stat-icon blue" />
                <span className="stat-value">{userStats.pointsEarned}</span>
                <span className="stat-label">Points</span>
              </div>
              <div className="stat-item">
                <Target className="stat-icon purple" />
                <span className="stat-value">#{userStats.rank}</span>
                <span className="stat-label">Rank</span>
              </div>
            </div>
          </div>
          <div className="header-actions">
            <div className="notification-bell" onClick={handleToggleNotifications}>
              <Bell className="notification-icon" />
              {notificationCount > 0 && (
                <span className="notification-badge">
                  {notificationCount}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Enhanced Recognition Section */}
        <div className="recognition-hero-section">
          <div className="hero-content">
            <div className="hero-text">
              <h2 className="hero-title">
                <Gift className="hero-icon" />
                Recognition & Kudos
              </h2>
              <p className="hero-description">
                Celebrate achievements and recognize your colleagues' outstanding work
              </p>
              <div className="hero-stats">
                <div className="hero-stat">
                  <Users className="hero-stat-icon" />
                  <span>24 colleagues recognized this week</span>
                </div>
                <div className="hero-stat">
                  <TrendingUp className="hero-stat-icon" />
                  <span>Recognition up 35% this month</span>
                </div>
              </div>
            </div>
            <div className="hero-actions">
              <button onClick={handleOpenModal} className="give-recognition-btn primary">
                <Plus className="give-recognition-icon" />
                Give Recognition
                <Sparkles className="btn-sparkle" />
              </button>
              <div className="quick-actions">
                <button className="quick-action-btn">
                  <Heart className="quick-icon" />
                  Quick Thanks
                </button>
                <button className="quick-action-btn">
                  <Award className="quick-icon" />
                  Nominate
                </button>
                <button className="quick-action-btn">
                  <Star className="quick-icon" />
                  Celebrate
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Grid Layout */}
        <div className="main-grid">
          {/* Left Column */}
          <div className="main-left-column">
            <RecognitionFeed />
          </div>

          {/* Right Column */}
          <div className="main-right-column">
            <TopRecipients />
            <MonthlyStats />
          </div>
        </div>
      </div>

      {/* Give Recognition Modal */}
      <GiveRecognitionModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />

      {/* Notifications Dropdown */}
      <NotificationsDropdown
        isOpen={isNotificationsOpen}
        onClose={handleCloseNotifications}
        notificationCount={notificationCount}
        onMarkAllRead={handleMarkAllRead}
        onViewAll={handleViewAllNotifications}
      />

      {/* Notifications Page */}
      <NotificationsPage
        isOpen={isNotificationsPageOpen}
        onClose={handleCloseNotificationsPage}
      />
    </div>
  )
}

export default MainContent
