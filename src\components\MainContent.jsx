import { useState } from 'react'
import { Plus, Bell } from 'lucide-react'
import RecognitionFeed from './RecognitionFeed'
import TopRecipients from './TopRecipients'
import MonthlyStats from './MonthlyStats'
import GiveRecognitionModal from './GiveRecognitionModal'
import NotificationsDropdown from './NotificationsDropdown'
import NotificationsPage from './NotificationsPage'
import './MainContent.css'

const MainContent = () => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false)
  const [isNotificationsPageOpen, setIsNotificationsPageOpen] = useState(false)
  const [notificationCount, setNotificationCount] = useState(2)

  const handleOpenModal = () => {
    console.log('Opening Give Recognition Modal')
    setIsModalOpen(true)
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
  }

  const handleToggleNotifications = () => {
    setIsNotificationsOpen(!isNotificationsOpen)
  }

  const handleCloseNotifications = () => {
    setIsNotificationsOpen(false)
  }

  const handleMarkAllRead = () => {
    setNotificationCount(0)
  }

  const handleViewAllNotifications = () => {
    setIsNotificationsOpen(false)
    setIsNotificationsPageOpen(true)
  }

  const handleCloseNotificationsPage = () => {
    setIsNotificationsPageOpen(false)
  }

  return (
    <div className="main-content">
      <div className="main-container">
        {/* Header */}
        <div className="main-header">
          <div>
            <h1 className="main-title">Recognition & Kudos</h1>
            <p className="main-subtitle">Celebrate achievements and recognize your colleagues</p>
          </div>
          <div className="notification-bell" onClick={handleToggleNotifications}>
            <Bell className="notification-icon" />
            {notificationCount > 0 && (
              <span className="notification-badge">
                {notificationCount}
              </span>
            )}
          </div>
        </div>

        {/* Give Recognition Button */}
        <div className="give-recognition-section">
          <button onClick={handleOpenModal} className="give-recognition-btn">
            <Plus className="give-recognition-icon" />
            Give Recognition
          </button>
        </div>

        {/* Main Grid Layout */}
        <div className="main-grid">
          {/* Left Column */}
          <div className="main-left-column">
            <RecognitionFeed />
          </div>

          {/* Right Column */}
          <div className="main-right-column">
            <TopRecipients />
            <MonthlyStats />
          </div>
        </div>
      </div>

      {/* Debug info */}
      {isModalOpen && (
        <div style={{ position: 'fixed', top: '10px', right: '10px', background: 'red', color: 'white', padding: '10px', zIndex: 10000 }}>
          Modal should be open: {isModalOpen.toString()}
        </div>
      )}

      {/* Give Recognition Modal */}
      <GiveRecognitionModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />

      {/* Notifications Dropdown */}
      <NotificationsDropdown
        isOpen={isNotificationsOpen}
        onClose={handleCloseNotifications}
        notificationCount={notificationCount}
        onMarkAllRead={handleMarkAllRead}
        onViewAll={handleViewAllNotifications}
      />

      {/* Notifications Page */}
      <NotificationsPage
        isOpen={isNotificationsPageOpen}
        onClose={handleCloseNotificationsPage}
      />
    </div>
  )
}

export default MainContent
