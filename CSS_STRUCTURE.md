# CSS Structure for HR Social Hub

## Overview
This project uses a component-based CSS architecture where each React component has its own dedicated CSS file. This approach provides better organization, maintainability, and prevents style conflicts.

## File Structure

```
src/
├── index.css                    # Global styles and Tailwind imports
├── components/
│   ├── Sidebar.jsx             # Sidebar component
│   ├── Sidebar.css             # Sidebar-specific styles
│   ├── MainContent.jsx         # Main content area
│   ├── MainContent.css         # Main content styles
│   ├── MyAchievements.jsx      # Achievements grid component
│   ├── MyAchievements.css      # Achievement card styles
│   ├── RecognitionFeed.jsx     # Recognition feed component
│   ├── RecognitionFeed.css     # Feed item styles
│   ├── TopRecipients.jsx       # Top recipients leaderboard
│   ├── TopRecipients.css       # Leaderboard styles
│   ├── MonthlyStats.jsx        # Monthly statistics component
│   └── MonthlyStats.css        # Statistics chart styles
```

## CSS Architecture

### 1. Global Styles (`src/index.css`)
- Tailwind CSS imports
- Global font family
- Base app layout styles
- CSS reset and box-sizing

### 2. Component-Specific CSS Files

#### Sidebar.css
- Navigation menu styling
- Active/inactive states
- Badge indicators
- Profile section layout

#### MainContent.css
- Main container layout
- Header styling
- Grid system for responsive layout
- Button styles

#### MyAchievements.css
- Achievement card grid
- Icon styling
- Hover effects
- Responsive breakpoints

#### RecognitionFeed.css
- Feed item layout
- Avatar styling
- Action buttons
- Message formatting

#### TopRecipients.css
- Leaderboard item layout
- Ranking indicators
- Avatar color variants
- Statistics display

#### MonthlyStats.css
- Progress bar styling
- Statistics layout
- Color coding for different metrics

## Design Principles

1. **Component Isolation**: Each component has its own CSS file
2. **BEM-like Naming**: Clear, descriptive class names
3. **Responsive Design**: Mobile-first approach with breakpoints
4. **Consistent Spacing**: Standardized padding and margins
5. **Color System**: Consistent color palette across components
6. **Typography**: Unified font sizing and weights

## Color Palette

- **Primary Blue**: #3b82f6
- **Secondary Gray**: #6b7280
- **Background Gray**: #f9fafb
- **Border Gray**: #e5e7eb
- **Text Dark**: #111827
- **Text Light**: #6b7280
- **Success Green**: #10b981
- **Warning Orange**: #f97316
- **Error Red**: #ef4444

## Responsive Breakpoints

- **Mobile**: < 768px
- **Tablet**: 768px - 1279px
- **Desktop**: ≥ 1280px

## Usage

Each component imports its corresponding CSS file:

```jsx
import './ComponentName.css'
```

This ensures styles are scoped to the component and loaded only when needed.
