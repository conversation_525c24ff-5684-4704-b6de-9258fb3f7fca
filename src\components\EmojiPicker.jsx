 import { useState } from 'react'

const EmojiPicker = ({ onEmojiSelect, onClose }) => {
  const emojis = [
    '👏', '🎉', '🌟', '💪', '🚀', '🔥', '💯', '✨',
    '👍', '❤️', '🙌', '💡', '🏆', '🎯', '⭐', '🎊',
    '😊', '😍', '🤩', '🥳', '👌', '💖', '🌈', '🎈'
  ]

  const gifs = [
    { id: 1, url: 'https://media.giphy.com/media/26u4cqiYI30juCOGY/giphy.gif', title: 'Celebration' },
    { id: 2, url: 'https://media.giphy.com/media/3oz8xAFtqoOUUrsh7W/giphy.gif', title: 'Applause' },
    { id: 3, url: 'https://media.giphy.com/media/l0MYt5jPR6QX5pnqM/giphy.gif', title: 'High Five' },
    { id: 4, url: 'https://media.giphy.com/media/26u4lOMA8JKSnL9Uk/giphy.gif', title: 'Thumbs Up' },
  ]

  const [activeTab, setActiveTab] = useState('emojis')

  return (
    <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-[1001]" onClick={onClose}>
      <div className="bg-white rounded-xl shadow-2xl w-80 max-h-96 overflow-hidden transform transition-all duration-300 scale-100" onClick={(e) => e.stopPropagation()}>
        <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-gray-100">
          <div className="flex gap-2">
            <button
              className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 ${
                activeTab === 'emojis'
                  ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-md'
                  : 'bg-transparent text-gray-600 hover:bg-gray-200'
              }`}
              onClick={() => setActiveTab('emojis')}
            >
              😊 Emojis
            </button>
            <button
              className={`px-3 py-1.5 rounded-lg text-sm font-medium transition-all duration-200 ${
                activeTab === 'gifs'
                  ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-md'
                  : 'bg-transparent text-gray-600 hover:bg-gray-200'
              }`}
              onClick={() => setActiveTab('gifs')}
            >
              🎬 GIFs
            </button>
          </div>
          <button
            onClick={onClose}
            className="w-6 h-6 flex items-center justify-center text-gray-500 hover:bg-gray-200 rounded-md transition-colors duration-200"
          >
            ×
          </button>
        </div>

        <div className="p-4 max-h-72 overflow-y-auto">
          {activeTab === 'emojis' ? (
            <div className="grid grid-cols-8 gap-2">
              {emojis.map((emoji, index) => (
                <button
                  key={index}
                  className="p-2 text-xl rounded-lg hover:bg-gray-100 transition-colors duration-200 flex items-center justify-center"
                  onClick={() => {
                    onEmojiSelect(emoji)
                    onClose()
                  }}
                >
                  {emoji}
                </button>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-2 gap-3">
              {gifs.map((gif) => (
                <div
                  key={gif.id}
                  className="cursor-pointer rounded-lg overflow-hidden transition-transform duration-200 hover:scale-105 bg-gray-100"
                  onClick={() => {
                    onEmojiSelect(`![${gif.title}](${gif.url})`)
                    onClose()
                  }}
                >
                  <img src={gif.url} alt={gif.title} className="w-full h-20 object-cover" />
                  <div className="p-2 text-xs text-gray-600 text-center">{gif.title}</div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default EmojiPicker
