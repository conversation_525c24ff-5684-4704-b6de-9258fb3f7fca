/* 🎉 Enhanced RecognitionFeed with Attractive Colors & Modern Design */
.recognition-feed-card {
  background: linear-gradient(145deg,
    rgba(255, 255, 255, 0.95),
    rgba(255, 255, 255, 0.85));
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.3);
  overflow: hidden;
  position: relative;
}

.recognition-feed-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
              radial-gradient(circle at 80% 80%, rgba(168, 85, 247, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

/* Enhanced Header */
.recognition-feed-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  background: linear-gradient(135deg,
    rgba(102, 126, 234, 0.1),
    rgba(168, 85, 247, 0.05));
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 1;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  width: 24px;
  height: 24px;
  color: #f59e0b;
  animation: sparkleRotate 3s ease-in-out infinite;
}

@keyframes sparkleRotate {
  0%, 100% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
}

.recognition-feed-title {
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
}

.recognition-count {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
  background: rgba(107, 114, 128, 0.1);
  padding: 4px 8px;
  border-radius: 12px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* View Controls */
.view-controls {
  display: flex;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  padding: 4px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.view-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 8px;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-btn:hover {
  background: rgba(102, 126, 234, 0.1);
}

.view-btn.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.view-icon {
  width: 16px;
  height: 16px;
}

/* Sort Select */
.sort-select {
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sort-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Enhanced Filter Button */
.filter-toggle-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.filter-toggle-btn:hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
  color: #667eea;
}

.filter-toggle-btn.active {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-color: #667eea;
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.filter-icon {
  width: 16px;
  height: 16px;
}

.filter-count {
  background: rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 50%;
  margin-left: 4px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: filterPulse 2s ease-in-out infinite;
}

@keyframes filterPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Filter Panel */
.filter-panel {
  padding: 16px 24px;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.filter-row {
  display: flex;
  align-items: end;
  gap: 16px;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
  min-width: 150px;
}

.filter-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
  color: #84acee;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filter-label-icon {
  width: 12px;
  height: 12px;
  color: #6b7280;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background-color: white;
  color: #718fc0;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(111, 147, 204, 0.1);
}

.clear-filters-btn {
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  height: fit-content;
}

.clear-filters-btn:hover {
  background-color: #dc2626;
}

/* Enhanced Recognition Feed Content */
.recognition-feed-content {
  padding: 24px;
  position: relative;
  z-index: 1;
}

/* Enhanced Recognition Items */
.recognition-item {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  padding: 24px;
  margin-bottom: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(20px);
}

.recognition-item.animate-in {
  opacity: 1;
  transform: translateY(0);
  animation: slideInUp 0.6s ease-out forwards;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.recognition-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

/* Priority Indicator */
.priority-indicator {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: priorityGlow 2s ease-in-out infinite;
}

.priority-indicator.high {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.priority-icon {
  width: 16px;
  height: 16px;
  color: white;
}

@keyframes priorityGlow {
  0%, 100% { box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3); }
  50% { box-shadow: 0 8px 25px rgba(239, 68, 68, 0.5); }
}

/* Color Variants */
.recognition-item.gold { border-left: 4px solid #f59e0b; }
.recognition-item.blue { border-left: 4px solid #3b82f6; }
.recognition-item.purple { border-left: 4px solid #8b5cf6; }
.recognition-item.green { border-left: 4px solid #10b981; }
.recognition-item.orange { border-left: 4px solid #f97316; }

.recognition-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.recognition-item.gold::before { background: radial-gradient(circle at 50% 50%, rgba(245, 158, 11, 0.1) 0%, transparent 70%); }
.recognition-item.blue::before { background: radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.1) 0%, transparent 70%); }
.recognition-item.purple::before { background: radial-gradient(circle at 50% 50%, rgba(139, 92, 246, 0.1) 0%, transparent 70%); }
.recognition-item.green::before { background: radial-gradient(circle at 50% 50%, rgba(16, 185, 129, 0.1) 0%, transparent 70%); }
.recognition-item.orange::before { background: radial-gradient(circle at 50% 50%, rgba(249, 115, 22, 0.1) 0%, transparent 70%); }

.recognition-item:hover::before {
  opacity: 1;
}

/* Enhanced Recognition Header */
.recognition-header {
  display: grid;
  grid-template-columns: auto 1fr auto auto;
  gap: 16px;
  align-items: center;
  margin-bottom: 16px;
}

.recognition-avatars {
  display: flex;
  align-items: center;
  gap: 8px;
}

.recognition-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.recognition-avatar.from.gold { background: linear-gradient(135deg, #f59e0b, #d97706); }
.recognition-avatar.from.blue { background: linear-gradient(135deg, #3b82f6, #2563eb); }
.recognition-avatar.from.purple { background: linear-gradient(135deg, #8b5cf6, #7c3aed); }
.recognition-avatar.from.green { background: linear-gradient(135deg, #10b981, #059669); }
.recognition-avatar.from.orange { background: linear-gradient(135deg, #f97316, #ea580c); }

.recognition-avatar.to.gold { background: linear-gradient(135deg, #fbbf24, #f59e0b); }
.recognition-avatar.to.blue { background: linear-gradient(135deg, #60a5fa, #3b82f6); }
.recognition-avatar.to.purple { background: linear-gradient(135deg, #a78bfa, #8b5cf6); }
.recognition-avatar.to.green { background: linear-gradient(135deg, #34d399, #10b981); }
.recognition-avatar.to.orange { background: linear-gradient(135deg, #fb923c, #f97316); }

.recognition-avatar-text {
  color: white;
  font-weight: 700;
  font-size: 14px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.recognition-arrow {
  width: 20px;
  height: 20px;
  color: #9ca3af;
  animation: arrowPulse 2s ease-in-out infinite;
}

@keyframes arrowPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.recognition-names {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.recognition-from,
.recognition-to {
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.recognition-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.recognition-timestamp {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

.recognition-badges {
  display: flex;
  gap: 6px;
}

.category-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.category-badge.gold { background: rgba(245, 158, 11, 0.2); color: #92400e; }
.category-badge.blue { background: rgba(59, 130, 246, 0.2); color: #1e40af; }
.category-badge.purple { background: rgba(139, 92, 246, 0.2); color: #6d28d9; }
.category-badge.green { background: rgba(16, 185, 129, 0.2); color: #047857; }
.category-badge.orange { background: rgba(249, 115, 22, 0.2); color: #c2410c; }

.points-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.points-icon {
  width: 12px;
  height: 12px;
}

/* Actions Menu */
.recognition-actions-menu {
  display: flex;
  gap: 4px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: scale(1.05);
}

.action-btn.bookmark.active {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
}

.action-btn.share:hover {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
}

.action-icon {
  width: 16px;
  height: 16px;
}

/* Enhanced Message */
.recognition-message {
  font-size: 16px;
  color: #374151;
  margin: 0 0 16px 0;
  line-height: 1.6;
  font-weight: 400;
}

/* Achievement Badges */
.achievement-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 16px 0;
}

.achievement-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 11px;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
}

.achievement-badge.gold {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(217, 119, 6, 0.1));
  color: #92400e;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.achievement-badge.blue {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(37, 99, 235, 0.1));
  color: #1e40af;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.achievement-badge.purple {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(124, 58, 237, 0.1));
  color: #6d28d9;
  border: 1px solid rgba(139, 92, 246, 0.3);
}

.achievement-badge.green {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(5, 150, 105, 0.1));
  color: #047857;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.achievement-badge.orange {
  background: linear-gradient(135deg, rgba(249, 115, 22, 0.2), rgba(234, 88, 12, 0.1));
  color: #c2410c;
  border: 1px solid rgba(249, 115, 22, 0.3);
}

.achievement-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.badge-icon {
  width: 12px;
  height: 12px;
}

/* Enhanced Recognition Actions */
.recognition-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
  padding: 16px 0;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.reaction-buttons {
  display: flex;
  gap: 8px;
}

.reaction-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
  font-weight: 500;
}

.reaction-btn:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.reaction-btn.heart.active {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.reaction-btn.thumbs.active {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
  color: white;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.reaction-btn.star.active {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.reaction-icon {
  width: 16px;
  height: 16px;
  transition: all 0.3s ease;
}

.reaction-icon.filled {
  fill: currentColor;
}

.reaction-count {
  font-weight: 600;
  min-width: 16px;
  text-align: center;
}

.recognition-action {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 8px 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  backdrop-filter: blur(10px);
}

.recognition-action:hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-2px);
}

.recognition-action-icon {
  width: 16px;
  height: 16px;
}

/* Comments Section */
.comments-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.comments-list {
  margin-bottom: 12px;
}

.comment-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}

.comment-avatar {
  width: 24px;
  height: 24px;
  background-color: #6b7280;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  flex-shrink: 0;
}

.comment-avatar span {
  color: white;
  font-size: 10px;
  font-weight: 600;
}

.comment-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.comment-author {
  font-size: 12px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 2px;
}

.comment-text {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.4;
}

.add-comment {
  margin-top: 12px;
}

.comment-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.comment-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 20px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s ease;
}

.comment-input:focus {
  border-color: #3472d6;
}

.comment-send-btn {
  background-color: #3b82f6;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.comment-send-btn:hover:not(:disabled) {
  background-color: #2563eb;
}

.comment-send-btn:disabled {
  background-color: #d1d5db;
  cursor: not-allowed;
}

.send-icon {
  width: 14px;
  height: 14px;
  color: white;
}

/* Recognition Attachments */
.recognition-attachments {
  margin: 12px 0;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.attachment-preview {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.attachment-image {
  width: 120px;
  height: 120px;
  object-fit: cover;
  display: block;
}

.attachment-file {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #f9fafb;
  gap: 8px;
}

.attachment-file-icon {
  width: 16px;
  height: 16px;
  color: #6b7280;
}

.attachment-file-name {
  font-size: 12px;
  color: #374151;
}

/* Comment Upload Functionality */
.comment-input-container {
  display: flex;
  align-items: flex-end;
  gap: 8px;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.upload-btn {
  background-color: #f3f4f6;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.upload-btn:hover {
  background-color: #e5e7eb;
}

.upload-icon {
  width: 14px;
  height: 14px;
  color: #6b7280;
}

/* Comment Attachments Preview */
.comment-attachments-preview {
  margin: 8px 0;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.comment-attachment-item {
  position: relative;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.comment-attachment-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  display: block;
}

.comment-attachment-file {
  display: flex;
  align-items: center;
  padding: 6px 8px;
  background-color: #f9fafb;
  gap: 6px;
  min-width: 60px;
}

.comment-attachment-icon {
  width: 12px;
  height: 12px;
  color: #6b7280;
}

.comment-attachment-name {
  font-size: 10px;
  color: #354e75;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.remove-attachment-btn {
  position: absolute;
  top: 2px;
  right: 2px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  line-height: 1;
}

.remove-attachment-btn:hover {
  background-color: rgba(0, 0, 0, 0.9);
}

/* Comment Attachments Display */
.comment-attachments {
  margin-top: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.comment-attachment-display {
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.comment-attachment-display-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  display: block;
}

.comment-attachment-display-file {
  display: flex;
  align-items: center;
  padding: 4px 6px;
  background-color: #f9fafb;
  gap: 4px;
}

.comment-attachment-display-icon {
  width: 12px;
  height: 12px;
  color: #6b7280;
}

.comment-attachment-display-file span {
  font-size: 10px;
  color: #374151;
  max-width: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Responsive Design for Filters */
@media (max-width: 768px) {
  .recognition-feed-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
  }

  .header-left {
    width: 100%;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .filter-panel {
    padding: 12px 16px;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filter-group {
    min-width: auto;
    width: 100%;
  }

  .clear-filters-btn {
    width: 100%;
  }
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
  .header-actions {
    flex-wrap: wrap;
    gap: 8px;
  }

  .recognition-item {
    padding: 20px;
  }

  .recognition-header {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .recognition-avatars {
    justify-self: center;
  }

  .recognition-names {
    text-align: center;
  }

  .recognition-meta {
    align-items: center;
  }

  .recognition-actions-menu {
    justify-self: center;
  }
}

@media (max-width: 768px) {
  .recognition-feed-header {
    padding: 16px;
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-left {
    width: 100%;
    justify-content: space-between;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .view-controls {
    order: 3;
  }

  .sort-select {
    order: 2;
    flex: 1;
  }

  .filter-toggle-btn {
    order: 1;
  }

  .recognition-feed-content {
    padding: 16px;
  }

  .recognition-item {
    padding: 16px;
    margin-bottom: 16px;
  }

  .recognition-avatar {
    width: 36px;
    height: 36px;
  }

  .recognition-avatar-text {
    font-size: 12px;
  }

  .recognition-message {
    font-size: 14px;
  }

  .achievement-badges {
    justify-content: center;
  }

  .recognition-actions {
    flex-direction: column;
    gap: 12px;
  }

  .reaction-buttons {
    justify-content: center;
    width: 100%;
  }

  .recognition-action {
    width: 100%;
    justify-content: center;
  }

  .filter-panel {
    padding: 16px;
  }

  .filter-row {
    flex-direction: column;
    gap: 12px;
  }

  .filter-group {
    min-width: auto;
    width: 100%;
  }
}

@media (max-width: 480px) {
  .recognition-feed-header {
    padding: 12px;
  }

  .title-section {
    gap: 8px;
  }

  .recognition-feed-title {
    font-size: 18px;
  }

  .recognition-count {
    font-size: 12px;
  }

  .header-actions {
    gap: 8px;
  }

  .view-controls {
    padding: 2px;
  }

  .view-btn {
    padding: 6px 8px;
  }

  .sort-select {
    padding: 6px 8px;
    font-size: 11px;
  }

  .filter-toggle-btn {
    padding: 6px 12px;
    font-size: 11px;
  }

  .recognition-feed-content {
    padding: 12px;
  }

  .recognition-item {
    padding: 12px;
  }

  .recognition-avatar {
    width: 32px;
    height: 32px;
  }

  .recognition-avatar-text {
    font-size: 11px;
  }

  .recognition-message {
    font-size: 13px;
  }

  .achievement-badge {
    padding: 4px 8px;
    font-size: 10px;
  }

  .reaction-btn {
    padding: 6px 8px;
    font-size: 11px;
  }

  .recognition-action {
    padding: 6px 12px;
    font-size: 12px;
  }

  .category-badge,
  .points-badge {
    font-size: 9px;
    padding: 3px 6px;
  }

  .priority-indicator {
    width: 28px;
    height: 28px;
    top: 12px;
    right: 12px;
  }

  .priority-icon {
    width: 14px;
    height: 14px;
  }
}

/* Compact View Mode */
.recognition-item.compact {
  padding: 16px;
  margin-bottom: 12px;
}

.recognition-item.compact .recognition-header {
  grid-template-columns: auto 1fr auto;
  gap: 12px;
}

.recognition-item.compact .recognition-avatars {
  gap: 4px;
}

.recognition-item.compact .recognition-avatar {
  width: 32px;
  height: 32px;
}

.recognition-item.compact .recognition-avatar-text {
  font-size: 11px;
}

.recognition-item.compact .recognition-message {
  font-size: 14px;
  margin-bottom: 12px;
}

.recognition-item.compact .achievement-badges {
  margin: 8px 0;
}

.recognition-item.compact .achievement-badge {
  padding: 4px 8px;
  font-size: 10px;
}

.recognition-item.compact .recognition-actions {
  padding: 12px 0;
}

.recognition-item.compact .reaction-btn {
  padding: 6px 8px;
  font-size: 11px;
}

/* Animation Delays for Staggered Effect */
.recognition-item:nth-child(1) { animation-delay: 0.1s; }
.recognition-item:nth-child(2) { animation-delay: 0.2s; }
.recognition-item:nth-child(3) { animation-delay: 0.3s; }
.recognition-item:nth-child(4) { animation-delay: 0.4s; }
.recognition-item:nth-child(5) { animation-delay: 0.5s; }
.recognition-item:nth-child(6) { animation-delay: 0.6s; }
