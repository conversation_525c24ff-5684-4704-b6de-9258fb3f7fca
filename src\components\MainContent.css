/* MainContent Component Styles */
.main-content {
  flex: 1;
  overflow: auto;
  background-color: #f9fafb;
}

.main-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 32px;
}

.main-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 32px;
}

.main-title {
  font-size: 30px;
  font-weight: 700;
  color: #111827;
  margin: 0 0 8px 0;
}

.main-subtitle {
  color: #6b7280;
  margin: 0;
}

.notification-bell {
  position: relative;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-bell:hover {
  background-color: #f3f4f6;
}

.notification-icon {
  width: 24px;
  height: 24px;
  color: #9ca3af;
}

.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background-color: #ef4444;
  color: white;
  font-size: 12px;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.give-recognition-section {
  margin-bottom: 32px;
}

.give-recognition-btn {
  background-color: #1ca0cd;
  color: white;
  padding: 12px 32px;
  border-radius: 8px;
  font-weight: 600;
  display: flex;
  align-items: center;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.give-recognition-btn:hover {
  background-color: #1fc9cc;
}

.give-recognition-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.main-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 32px;
}

@media (min-width: 1280px) {
  .main-grid {
    grid-template-columns: 3fr 1fr;
  }
}

.main-left-column {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.main-right-column {
  display: flex;
  flex-direction: column;
  gap: 32px;
}
