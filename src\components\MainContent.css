/* 🌟 Enhanced MainContent with Attractive Colors & Modern Design */
.main-content {
  flex: 1;
  overflow: auto;
  background: linear-gradient(135deg,
    #667eea 0%,
    #764ba2 25%,
    #f093fb 50%,
    #f5576c 75%,
    #4facfe 100%);
  min-height: 100vh;
  position: relative;
}

.main-content::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="sparkles" x="0" y="0" width="25" height="25" patternUnits="userSpaceOnUse"><circle cx="5" cy="5" r="1" fill="rgba(255,255,255,0.3)"/><circle cx="20" cy="15" r="0.5" fill="rgba(255,255,255,0.4)"/><circle cx="15" cy="20" r="0.8" fill="rgba(255,255,255,0.2)"/></pattern></defs><rect width="100" height="100" fill="url(%23sparkles)"/></svg>');
  opacity: 0.1;
  animation: sparkleMove 20s linear infinite;
  pointer-events: none;
}

@keyframes sparkleMove {
  0% { transform: translateX(0) translateY(0); }
  100% { transform: translateX(-25px) translateY(-25px); }
}

.main-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 32px;
  position: relative;
  z-index: 1;
}

/* Enhanced Header */
.main-header {
  background: linear-gradient(145deg,
    rgba(255, 255, 255, 0.95),
    rgba(255, 255, 255, 0.85));
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  overflow: hidden;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 40px;
  flex: 1;
}

.greeting-section {
  flex: 1;
}

.main-title {
  font-size: 36px;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
  animation: titleGlow 3s ease-in-out infinite;
}

@keyframes titleGlow {
  0%, 100% { filter: brightness(1); }
  50% { filter: brightness(1.1); }
}

.title-icon {
  width: 32px;
  height: 32px;
  color: #f59e0b;
  animation: sparkleRotate 3s ease-in-out infinite;
}

@keyframes sparkleRotate {
  0%, 100% { transform: rotate(0deg) scale(1); }
  50% { transform: rotate(180deg) scale(1.1); }
}

.main-subtitle {
  color: #6b7280;
  font-size: 18px;
  font-weight: 500;
  margin: 0;
  animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* User Stats Preview */
.user-stats-preview {
  display: flex;
  gap: 24px;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  min-width: 80px;
}

.stat-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
}

.stat-icon.gold { color: #f59e0b; }
.stat-icon.blue { color: #3b82f6; }
.stat-icon.purple { color: #8b5cf6; }

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: #1f2937;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
}

/* Header Actions */
.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.notification-bell {
  position: relative;
  cursor: pointer;
  padding: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification-bell:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.notification-icon {
  width: 24px;
  height: 24px;
  color: #667eea;
  animation: bellRing 2s ease-in-out infinite;
}

@keyframes bellRing {
  0%, 100% { transform: rotate(0deg); }
  10%, 30% { transform: rotate(-10deg); }
  20%, 40% { transform: rotate(10deg); }
}

.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  font-size: 11px;
  font-weight: 600;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);
  animation: badgePulse 2s ease-in-out infinite;
}

@keyframes badgePulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Enhanced Recognition Hero Section */
.recognition-hero-section {
  background: linear-gradient(145deg,
    rgba(255, 255, 255, 0.95),
    rgba(255, 255, 255, 0.85));
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 40px;
  margin-bottom: 32px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
}

.recognition-hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 80%, rgba(168, 85, 247, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 40% 60%, rgba(236, 72, 153, 0.1) 0%, transparent 50%);
  animation: heroGlow 6s ease-in-out infinite;
  pointer-events: none;
}

@keyframes heroGlow {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 0.8; }
}

.hero-content {
  display: flex;
  align-items: center;
  gap: 40px;
  position: relative;
  z-index: 1;
}

.hero-text {
  flex: 1;
}

.hero-title {
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.hero-icon {
  width: 32px;
  height: 32px;
  color: #f59e0b;
  animation: heroIconFloat 3s ease-in-out infinite;
}

@keyframes heroIconFloat {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-5px) rotate(5deg); }
}

.hero-description {
  font-size: 18px;
  color: #6b7280;
  margin: 0 0 24px 0;
  line-height: 1.6;
}

.hero-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.hero-stat {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #6b7280;
}

.hero-stat-icon {
  width: 16px;
  height: 16px;
  color: #667eea;
}

/* Enhanced Give Recognition Button */
.give-recognition-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 32px;
  border: none;
  border-radius: 20px;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.give-recognition-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  color: white;
}

.give-recognition-btn:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
}

.give-recognition-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.give-recognition-btn:hover::before {
  left: 100%;
}

.give-recognition-icon {
  width: 20px;
  height: 20px;
  z-index: 1;
  position: relative;
}

.btn-sparkle {
  width: 16px;
  height: 16px;
  z-index: 1;
  position: relative;
  animation: sparkleRotate 2s ease-in-out infinite;
}

.hero-actions {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: flex-end;
}

/* Quick Actions */
.quick-actions {
  display: flex;
  gap: 12px;
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  min-width: 80px;
}

.quick-action-btn:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  color: #374151;
}

.quick-icon {
  width: 20px;
  height: 20px;
  color: #667eea;
  transition: all 0.3s ease;
}

/* Main Grid Layout */
.main-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 32px;
  margin-top: 32px;
}

@media (min-width: 1280px) {
  .main-grid {
    grid-template-columns: 2fr 1fr;
    gap: 40px;
  }
}

.main-left-column {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.main-right-column {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
  .main-container {
    padding: 24px;
  }

  .main-header {
    padding: 24px;
  }

  .header-content {
    flex-direction: column;
    gap: 24px;
    align-items: flex-start;
  }

  .user-stats-preview {
    gap: 16px;
  }

  .recognition-hero-section {
    padding: 32px;
  }

  .hero-content {
    flex-direction: column;
    gap: 32px;
    text-align: center;
  }

  .hero-actions {
    align-items: center;
    width: 100%;
  }

  .give-recognition-btn {
    width: 100%;
    justify-content: center;
  }

  .quick-actions {
    justify-content: center;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .main-container {
    padding: 16px;
  }

  .main-header {
    padding: 20px;
    flex-direction: column;
    gap: 20px;
  }

  .header-content {
    width: 100%;
  }

  .header-actions {
    align-self: flex-end;
  }

  .main-title {
    font-size: 28px;
  }

  .main-subtitle {
    font-size: 16px;
  }

  .user-stats-preview {
    flex-wrap: wrap;
    gap: 12px;
  }

  .stat-item {
    min-width: 70px;
    padding: 12px;
  }

  .recognition-hero-section {
    padding: 24px;
  }

  .hero-title {
    font-size: 24px;
  }

  .hero-description {
    font-size: 16px;
  }

  .quick-actions {
    flex-wrap: wrap;
    gap: 8px;
  }

  .quick-action-btn {
    min-width: 70px;
    padding: 10px 12px;
    font-size: 11px;
  }

  .main-grid {
    gap: 24px;
  }
}

@media (max-width: 480px) {
  .main-container {
    padding: 12px;
  }

  .main-header,
  .recognition-hero-section {
    padding: 16px;
  }

  .main-title {
    font-size: 24px;
  }

  .user-stats-preview {
    justify-content: center;
  }

  .hero-content {
    gap: 24px;
  }

  .hero-title {
    font-size: 20px;
  }

  .give-recognition-btn {
    padding: 14px 24px;
    font-size: 14px;
  }

  .quick-actions {
    grid-template-columns: repeat(3, 1fr);
    display: grid;
  }
}
