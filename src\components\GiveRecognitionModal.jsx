import { useState } from 'react'
import { X, Send, Users, Award, Lightbulb, Crown, Heart } from 'lucide-react'
import './GiveRecognitionModal.css'

const GiveRecognitionModal = ({ isOpen, onClose }) => {
  const [formData, setFormData] = useState({
    recipient: '',
    category: '',
    message: ''
  })
  const [showSuccess, setShowSuccess] = useState(false)

  const colleagues = [
    { id: 1, name: '<PERSON>', department: 'Engineering' },
    { id: 2, name: '<PERSON>', department: 'Design' },
    { id: 3, name: '<PERSON>', department: 'Marketing' },
    { id: 4, name: '<PERSON>', department: 'Sales' },
    { id: 5, name: '<PERSON>', department: 'HR' },
    { id: 6, name: '<PERSON>', department: 'Engineering' },
    { id: 7, name: '<PERSON>', department: 'Operations' },
  ]

  const categories = [
    { value: 'teamwork', label: 'Great Teamwork', icon: Users },
    { value: 'above-beyond', label: 'Going Above & Beyond', icon: Award },
    { value: 'innovation', label: 'Innovation', icon: Lightbulb },
    { value: 'leadership', label: 'Leadership', icon: Crown },
    { value: 'customer-focus', label: 'Customer Focus', icon: Heart },
  ]

  const handleSubmit = (e) => {
    e.preventDefault()
    if (!formData.recipient || !formData.category || !formData.message.trim()) {
      alert('Please fill in all required fields')
      return
    }

    // Simulate API call
    setTimeout(() => {
      setShowSuccess(true)
      setTimeout(() => {
        setShowSuccess(false)
        onClose()
        setFormData({
          recipient: '',
          category: '',
          message: ''
        })
      }, 2000)
    }, 500)
  }

  if (!isOpen) return null

  return (
    <div className="modal-overlay">
      <div className="modal-container compact">
        {showSuccess ? (
          <div className="success-message">
            <div className="success-icon">🎉</div>
            <h3>Recognition Sent!</h3>
            <p>You just made someone's day brighter!</p>
          </div>
        ) : (
          <>
            <div className="modal-header">
              <h2>🌟 Give Recognition</h2>
              <button onClick={onClose} className="close-button">
                <X className="close-icon" />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="modal-form">
              {/* Recipient */}
              <div className="form-group">
                <label className="form-label">
                  👤 Recipient <span className="required">*</span>
                </label>
                <select
                  value={formData.recipient}
                  onChange={(e) => setFormData(prev => ({ ...prev, recipient: e.target.value }))}
                  className="recipient-select"
                >
                  <option value="">Select a colleague</option>
                  {colleagues.map(colleague => (
                    <option key={colleague.id} value={colleague.name}>
                      {colleague.name} - {colleague.department}
                    </option>
                  ))}
                </select>
              </div>

              {/* Category */}
              <div className="form-group">
                <label className="form-label">
                  🏆 Category <span className="required">*</span>
                </label>
                <div className="category-grid">
                  {categories.map(category => {
                    const IconComponent = category.icon
                    return (
                      <div
                        key={category.value}
                        onClick={() => setFormData(prev => ({ ...prev, category: category.value }))}
                        className={`category-card ${formData.category === category.value ? 'selected' : ''}`}
                      >
                        <IconComponent className="category-icon" />
                        <span className="category-label">{category.label}</span>
                      </div>
                    )
                  })}
                </div>
              </div>

              {/* Message */}
              <div className="form-group">
                <label className="form-label">
                  💬 Message <span className="required">*</span>
                </label>
                <textarea
                  value={formData.message}
                  onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
                  placeholder="Write your message of appreciation..."
                  className="message-textarea"
                  rows={3}
                />
              </div>

              {/* Submit Button */}
              <div className="form-actions">
                <button type="button" onClick={onClose} className="cancel-button">
                  Cancel
                </button>
                <button type="submit" className="submit-button">
                  <Send className="button-icon" />
                  Send Recognition
                </button>
              </div>
            </form>
          </>
        )}
      </div>
    </div>
  )
}

export default GiveRecognitionModal