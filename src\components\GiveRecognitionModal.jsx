   import { useState } from 'react'
import { X, Smile, Users, Award, Lightbulb, Crown, Heart } from 'lucide-react'
import EmojiPicker from './EmojiPicker'
import './GiveRecognitionModal.css'

const GiveRecognitionModal = ({ isOpen, onClose }) => {
  const [formData, setFormData] = useState({
    recipient: '',
    category: '',
    message: '',
    points: 10,
    isAnonymous: false
  })
  const [showSuccess, setShowSuccess] = useState(false)
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)

  const colleagues = [
    { id: 1, name: '<PERSON>', department: 'Engineering' },
    { id: 2, name: '<PERSON>', department: 'Design' },
    { id: 3, name: '<PERSON>', department: 'Marketing' },
    { id: 4, name: '<PERSON>', department: 'Sales' },
    { id: 5, name: '<PERSON>', department: 'HR' },
    { id: 6, name: '<PERSON>', department: 'Engineering' },
    { id: 7, name: '<PERSON>', department: 'Operations' },
  ]

  const categories = [
    { value: 'teamwork', label: 'Great Teamwork', icon: Users, points: 10 },
    { value: 'above-beyond', label: 'Going Above & Beyond', icon: Award, points: 15 },
    { value: 'innovation', label: 'Innovation', icon: Lightbulb, points: 12 },
    { value: 'leadership', label: 'Leadership', icon: Crown, points: 15 },
    { value: 'customer-focus', label: 'Customer Focus', icon: Heart, points: 10 },
  ]

  const quickTemplates = [
    "Thank you for your amazing work on this project! 🎉",
    "Your dedication and effort really made a difference! 💪",
    "Great job on going above and beyond! ⭐",
    "Your teamwork skills are outstanding! 🤝",
    "Thanks for being so helpful and supportive! 🙏"
  ]

  const handleEmojiSelect = (emoji) => {
    setFormData(prev => ({
      ...prev,
      message: prev.message + emoji
    }))
  }

  const handleCategoryChange = (categoryValue) => {
    const selectedCategory = categories.find(cat => cat.value === categoryValue)
    setFormData(prev => ({
      ...prev,
      category: categoryValue,
      points: selectedCategory ? selectedCategory.points : 10
    }))
  }

  const handleTemplateSelect = (template) => {
    setFormData(prev => ({
      ...prev,
      message: template
    }))
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    if (!formData.recipient || !formData.category || !formData.message.trim()) {
      alert('Please fill in all required fields')
      return
    }

    // Simulate API call
    setTimeout(() => {
      setShowSuccess(true)
      setTimeout(() => {
        setShowSuccess(false)
        onClose()
        setFormData({
          recipient: '',
          category: '',
          message: '',
          points: 10,
          isAnonymous: false
        })
      }, 2000)
    }, 500)
  }

  if (!isOpen) return null

  return (
    <div className="modal-overlay">
      <div className="modal-container">
        {showSuccess ? (
          <div className="success-message">
            <div className="success-icon">✅</div>
            <h3>Recognition sent!</h3>
            <p>You just made someone's day brighter.</p>
          </div>
        ) : (
          <>
            <div className="modal-header">
              <h2>Give Recognition</h2>
              <button onClick={onClose} className="close-button">
                <X className="close-icon" />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="modal-form">
              {/* Recipient Selection */}
              <div className="form-group">
                <label className="form-label">
                  Recipient <span className="required">*</span>
                </label>
                <select
                  value={formData.recipient}
                  onChange={(e) => setFormData(prev => ({ ...prev, recipient: e.target.value }))}
                  className="recipient-select"
                >
                  <option value="">Select a colleague</option>
                  {colleagues.map(colleague => (
                    <option key={colleague.id} value={colleague.id}>
                      {colleague.name} - {colleague.department}
                    </option>
                  ))}
                </select>
              </div>

              {/* Anonymous Option */}
              <div className="form-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={formData.isAnonymous}
                    onChange={(e) => setFormData(prev => ({ ...prev, isAnonymous: e.target.checked }))}
                    className="checkbox-input"
                  />
                  <span className="checkbox-text">Send anonymously</span>
                </label>
              </div>

              {/* Category */}
              <div className="form-group">
                <label className="form-label">
                  Category / Reason <span className="required">*</span>
                </label>
                <select
                  value={formData.category}
                  onChange={(e) => handleCategoryChange(e.target.value)}
                  className="category-select"
                >
                  <option value="">Select a category</option>
                  {categories.map(category => (
                    <option key={category.value} value={category.value}>
                      {category.label} ({category.points} points)
                    </option>
                  ))}
                </select>
                {formData.category && (
                  <div className="points-display">
                    <span className="points-text">Recognition Points: {formData.points}</span>
                  </div>
                )}
              </div>

              {/* Quick Templates */}
              <div className="form-group">
                <label className="form-label">Quick Templates</label>
                <div className="template-buttons">
                  {quickTemplates.map((template, index) => (
                    <button
                      key={index}
                      type="button"
                      onClick={() => handleTemplateSelect(template)}
                      className="template-button"
                    >
                      {template.substring(0, 30)}...
                    </button>
                  ))}
                </div>
              </div>

              {/* Message */}
              <div className="form-group">
                <label className="form-label">
                  Message <span className="required">*</span>
                </label>
                <textarea
                  value={formData.message}
                  onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
                  placeholder="Write your message of appreciation…"
                  className="message-textarea"
                  rows={4}
                />
                <div className="message-actions">
                  <button
                    type="button"
                    className="emoji-button"
                    onClick={() => setShowEmojiPicker(true)}
                  >
                    <Smile className="emoji-icon" />
                    Add Emoji
                  </button>
                </div>
              </div>

              {/* Submit Button */}
              <div className="form-actions">
                <button type="button" onClick={onClose} className="cancel-button">
                  Cancel
                </button>
                <button type="submit" className="submit-button">
                  🎉 Send Recognition ({formData.points} points)
                </button>
              </div>
            </form>
          </>
        )}
      </div>

      {/* Emoji Picker */}
      {showEmojiPicker && (
        <EmojiPicker
          onEmojiSelect={handleEmojiSelect}
          onClose={() => setShowEmojiPicker(false)}
        />
      )}
    </div>
  )
}

export default GiveRecognitionModal