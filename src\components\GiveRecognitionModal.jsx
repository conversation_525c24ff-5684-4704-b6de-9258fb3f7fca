import { useState } from 'react'
import { X, Search, Upload, Image, Smile, Users, Award, Lightbulb, Crown, Heart, Send, Sparkles, Star } from 'lucide-react'
import EmojiPicker from './EmojiPicker'
import './GiveRecognitionModal.css'

const GiveRecognitionModal = ({ isOpen, onClose }) => {
  const [formData, setFormData] = useState({
    recipients: [],
    category: '',
    message: '',
    visibility: 'public',
    attachments: []
  })
  const [searchTerm, setSearchTerm] = useState('')
  const [showSuccess, setShowSuccess] = useState(false)
  const [showEmojiPicker, setShowEmojiPicker] = useState(false)

  const colleagues = [
    { id: 1, name: '<PERSON>', department: 'Engineering', avatar: 'SC' },
    { id: 2, name: '<PERSON>', department: 'Design', avatar: 'MT' },
    { id: 3, name: '<PERSON>', department: 'Marketing', avatar: 'E<PERSON>' },
    { id: 4, name: '<PERSON>', department: 'Sales', avatar: 'DK' },
    { id: 5, name: '<PERSON>', department: 'HR', avatar: 'LW' },
    { id: 6, name: '<PERSON>', department: 'Engineering', avatar: 'AM' },
    { id: 7, name: '<PERSON>', department: 'Operations', avatar: 'JW' },
  ]

  const categories = [
    { value: 'teamwork', label: 'Great Teamwork', icon: Users },
    { value: 'above-beyond', label: 'Going Above & Beyond', icon: Award },
    { value: 'innovation', label: 'Innovation', icon: Lightbulb },
    { value: 'leadership', label: 'Leadership', icon: Crown },
    { value: 'customer-focus', label: 'Customer Focus', icon: Heart },
  ]

  const visibilityOptions = [
    { value: 'public', label: 'Public (visible to all)' },
    { value: 'team', label: 'Team Only' },
    { value: 'private', label: 'Private (visible to recipient only)' }
  ]

  const filteredColleagues = colleagues.filter(colleague =>
    colleague.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    colleague.department.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleRecipientToggle = (colleague) => {
    setFormData(prev => ({
      ...prev,
      recipients: prev.recipients.find(r => r.id === colleague.id)
        ? prev.recipients.filter(r => r.id !== colleague.id)
        : [...prev.recipients, colleague]
    }))
  }

  const handleEmojiSelect = (emoji) => {
    setFormData(prev => ({
      ...prev,
      message: prev.message + emoji
    }))
  }

  const handleFileUpload = (event) => {
    const files = Array.from(event.target.files)
    setFormData(prev => ({
      ...prev,
      attachments: [...prev.attachments, ...files]
    }))
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    if (formData.recipients.length === 0 || !formData.category || !formData.message.trim()) {
      alert('Please fill in all required fields')
      return
    }

    // Simulate API call
    setTimeout(() => {
      setShowSuccess(true)
      setTimeout(() => {
        setShowSuccess(false)
        onClose()
        setFormData({
          recipients: [],
          category: '',
          message: '',
          visibility: 'public',
          attachments: []
        })
        setSearchTerm('')
      }, 2000)
    }, 500)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-purple-900/80 via-blue-900/80 to-pink-900/80 backdrop-blur-lg flex items-center justify-center z-50 p-4 transition-all duration-500">
      <div className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl max-w-4xl w-full max-h-[95vh] overflow-y-auto transform transition-all duration-500 scale-100 border border-white/20 relative">
        {/* Animated Background Gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-purple-50/50 to-pink-50/50 rounded-3xl"></div>

        {showSuccess ? (
          <div className="relative p-20 text-center bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50 rounded-3xl">
            {/* Floating Celebration Elements */}
            <div className="absolute inset-0 overflow-hidden rounded-3xl">
              {[...Array(12)].map((_, i) => (
                <div
                  key={i}
                  className="absolute animate-bounce"
                  style={{
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                    animationDelay: `${i * 0.2}s`,
                    animationDuration: `${2 + Math.random() * 2}s`
                  }}
                >
                  <span className="text-2xl">
                    {['🎉', '✨', '🌟', '💫', '🎊', '🏆'][Math.floor(Math.random() * 6)]}
                  </span>
                </div>
              ))}
            </div>

            <div className="relative z-10">
              <div className="w-32 h-32 bg-gradient-to-r from-emerald-400 via-green-500 to-teal-500 rounded-full flex items-center justify-center mx-auto mb-8 animate-pulse shadow-2xl">
                <Sparkles className="w-16 h-16 text-white animate-spin" style={{ animationDuration: '3s' }} />
              </div>
              <h3 className="text-4xl font-black bg-gradient-to-r from-emerald-600 via-green-600 to-teal-600 bg-clip-text text-transparent mb-6 animate-pulse">
                🎉 Recognition Sent Successfully! 🎉
              </h3>
              <p className="text-gray-700 text-xl font-medium mb-8">You just made someone's day brighter and more meaningful! 💖</p>
              <div className="flex justify-center mt-8">
                <div className="flex space-x-2">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className="w-8 h-8 text-yellow-400 fill-current animate-bounce"
                      style={{
                        animationDelay: `${i * 0.15}s`,
                        animationDuration: '1s'
                      }}
                    />
                  ))}
                </div>
              </div>
              <div className="mt-8 text-lg font-semibold text-emerald-600">
                ✨ Spreading positivity across the workplace! ✨
              </div>
            </div>
          </div>
        ) : (
          <>
            <div className="relative p-8 border-b border-white/20 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 rounded-t-3xl overflow-hidden">
              {/* Animated Background Pattern */}
              <div className="absolute inset-0 opacity-20">
                <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent animate-pulse"></div>
              </div>

              <div className="relative z-10 flex items-center justify-between">
                <div>
                  <h2 className="text-4xl font-black text-white mb-2 drop-shadow-lg">
                    ✨ Give Recognition
                  </h2>
                  <p className="text-white/90 text-lg font-medium">Celebrate achievements and spread positivity across your team</p>
                </div>
                <button
                  onClick={onClose}
                  className="p-4 hover:bg-white/20 rounded-full transition-all duration-300 group shadow-lg hover:shadow-xl backdrop-blur-sm border border-white/30"
                >
                  <X className="w-7 h-7 text-white group-hover:rotate-90 transition-all duration-300" />
                </button>
              </div>
            </div>

            <form onSubmit={handleSubmit} className="relative p-8 space-y-8 bg-gradient-to-br from-white/80 to-gray-50/80 backdrop-blur-sm">
              {/* Recipients Section */}
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <label className="text-2xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent flex items-center">
                    <Users className="w-7 h-7 mr-3 text-blue-500" />
                    Select Recipients <span className="text-red-500 ml-2 animate-pulse">*</span>
                  </label>
                  <div className="relative">
                    <select
                      value={formData.visibility}
                      onChange={(e) => setFormData(prev => ({ ...prev, visibility: e.target.value }))}
                      className="text-sm px-4 py-3 border-2 border-purple-300 rounded-xl bg-gradient-to-r from-purple-50 to-pink-50 text-purple-700 focus:ring-4 focus:ring-purple-500/30 focus:border-purple-500 transition-all duration-300 shadow-lg hover:shadow-xl font-medium"
                    >
                      {visibilityOptions.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="relative">
                  <Search className="absolute left-5 top-1/2 transform -translate-y-1/2 w-6 h-6 text-purple-500" />
                  <input
                    type="text"
                    placeholder="🔍 Search colleagues by name or department..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-14 pr-6 py-4 border-2 border-purple-300 rounded-2xl bg-gradient-to-r from-white to-purple-50 text-gray-700 placeholder-purple-400 focus:ring-4 focus:ring-purple-500/30 focus:border-purple-500 transition-all duration-300 shadow-lg hover:shadow-xl text-lg font-medium"
                  />
                </div>
                <div className="max-h-72 overflow-y-auto border-2 border-purple-200 rounded-2xl bg-gradient-to-br from-white to-purple-50/50 shadow-inner">
                  {filteredColleagues.map(colleague => (
                    <div
                      key={colleague.id}
                      onClick={() => handleRecipientToggle(colleague)}
                      className={`flex items-center p-4 cursor-pointer transition-all duration-300 border-b border-purple-100 last:border-b-0 hover:bg-gradient-to-r hover:from-purple-100 hover:to-pink-100 hover:shadow-lg hover:scale-[1.02] ${
                        formData.recipients.find(r => r.id === colleague.id)
                          ? 'bg-gradient-to-r from-purple-100 to-pink-100 border-l-4 border-l-purple-500 shadow-lg scale-[1.02]'
                          : ''
                      }`}
                    >
                      <div className={`w-14 h-14 bg-gradient-to-r ${colleague.color} rounded-full flex items-center justify-center text-white font-bold text-lg mr-4 shadow-xl ring-2 ring-white`}>
                        {colleague.avatar}
                      </div>
                      <div className="flex-1">
                        <div className="font-bold text-gray-900 text-lg">{colleague.name}</div>
                        <div className="text-sm text-purple-600 font-medium">{colleague.department}</div>
                      </div>
                      {formData.recipients.find(r => r.id === colleague.id) && (
                        <div className="w-8 h-8 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center shadow-lg animate-pulse">
                          <div className="w-3 h-3 bg-white rounded-full"></div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>

                {formData.recipients.length > 0 && (
                  <div className="p-6 bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl border-2 border-purple-200 shadow-lg">
                    <div className="text-lg font-bold text-purple-700 mb-4 flex items-center">
                      <Users className="w-5 h-5 mr-2 text-purple-500" />
                      Selected Recipients ({formData.recipients.length}) ✨
                    </div>
                    <div className="flex flex-wrap gap-3">
                      {formData.recipients.map(recipient => (
                        <div key={recipient.id} className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 group">
                          <span className="font-semibold">{recipient.name}</span>
                          <button
                            type="button"
                            onClick={() => handleRecipientToggle(recipient)}
                            className="ml-3 w-6 h-6 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-colors duration-200 group-hover:scale-110"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Category Section */}
              <div className="space-y-6">
                <label className="text-2xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent flex items-center">
                  <Award className="w-7 h-7 mr-3 text-purple-500" />
                  Category / Reason <span className="text-red-500 ml-2 animate-pulse">*</span>
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {categories.map(category => {
                    const IconComponent = category.icon
                    const isSelected = formData.category === category.value
                    return (
                      <div
                        key={category.value}
                        onClick={() => setFormData(prev => ({ ...prev, category: category.value }))}
                        className={`group relative p-6 rounded-2xl border-2 cursor-pointer transition-all duration-300 hover:shadow-2xl hover:scale-105 ${
                          isSelected
                            ? `${category.bgColor} ${category.borderColor} shadow-2xl scale-105 ring-4 ring-purple-400/30`
                            : 'border-gray-200 hover:border-purple-300 bg-white hover:bg-gradient-to-br hover:from-white hover:to-purple-50'
                        }`}
                      >
                        <div className="flex items-start space-x-4">
                          <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${category.gradient} flex items-center justify-center shadow-xl group-hover:shadow-2xl transition-all duration-300 ${isSelected ? 'animate-pulse' : ''}`}>
                            <IconComponent className="w-8 h-8 text-white" />
                          </div>
                          <div className="flex-1">
                            <div className={`font-bold text-xl mb-2 ${isSelected ? category.textColor : 'text-gray-900'}`}>
                              {category.label}
                            </div>
                            <div className={`text-sm font-medium ${isSelected ? category.textColor : 'text-gray-600'}`}>
                              {category.description}
                            </div>
                          </div>
                          {isSelected && (
                            <div className="absolute top-4 right-4 w-8 h-8 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center shadow-lg animate-bounce">
                              <div className="w-3 h-3 bg-white rounded-full"></div>
                            </div>
                          )}
                        </div>
                      </div>
                    )
                  })}
                </div>
              </div>

              {/* Message Section */}
              <div className="space-y-6">
                <label className="text-2xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent flex items-center">
                  <Heart className="w-7 h-7 mr-3 text-pink-500" />
                  Message <span className="text-red-500 ml-2 animate-pulse">*</span>
                </label>
                <textarea
                  value={formData.message}
                  onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
                  placeholder="✨ Write your heartfelt message of appreciation and recognition..."
                  className="w-full p-6 border-2 border-pink-300 rounded-2xl bg-gradient-to-br from-white to-pink-50 text-gray-700 placeholder-pink-400 focus:ring-4 focus:ring-pink-500/30 focus:border-pink-500 transition-all duration-300 shadow-lg hover:shadow-xl resize-none text-lg leading-relaxed font-medium"
                  rows={5}
                />
              </div>

              {/* Optional Features */}
              <div className="space-y-6">
                <label className="text-2xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent flex items-center">
                  <Sparkles className="w-7 h-7 mr-3 text-yellow-500" />
                  Add Fun Elements (Optional)
                </label>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <button
                    type="button"
                    className="group flex flex-col items-center justify-center p-8 border-2 border-yellow-300 rounded-2xl bg-gradient-to-br from-yellow-50 to-orange-50 hover:from-yellow-100 hover:to-orange-100 hover:border-orange-400 transition-all duration-300 hover:shadow-2xl hover:scale-105"
                    onClick={() => setShowEmojiPicker(true)}
                  >
                    <Smile className="w-12 h-12 mb-4 text-orange-500 group-hover:text-orange-600 transition-colors duration-200 group-hover:animate-bounce" />
                    <span className="text-lg font-bold text-gray-700 group-hover:text-orange-700">Add Emoji/GIF</span>
                    <span className="text-sm text-gray-500 mt-1">Express with emojis</span>
                  </button>
                  <label className="group flex flex-col items-center justify-center p-8 border-2 border-blue-300 rounded-2xl bg-gradient-to-br from-blue-50 to-cyan-50 hover:from-blue-100 hover:to-cyan-100 hover:border-cyan-400 transition-all duration-300 hover:shadow-2xl hover:scale-105 cursor-pointer">
                    <Image className="w-12 h-12 mb-4 text-cyan-500 group-hover:text-cyan-600 transition-colors duration-200 group-hover:animate-bounce" />
                    <span className="text-lg font-bold text-gray-700 group-hover:text-cyan-700">Add Image</span>
                    <span className="text-sm text-gray-500 mt-1">Upload a photo</span>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleFileUpload}
                      className="hidden"
                    />
                  </label>
                  <label className="group flex flex-col items-center justify-center p-8 border-2 border-purple-300 rounded-2xl bg-gradient-to-br from-purple-50 to-pink-50 hover:from-purple-100 hover:to-pink-100 hover:border-pink-400 transition-all duration-300 hover:shadow-2xl hover:scale-105 cursor-pointer">
                    <Upload className="w-12 h-12 mb-4 text-pink-500 group-hover:text-pink-600 transition-colors duration-200 group-hover:animate-bounce" />
                    <span className="text-lg font-bold text-gray-700 group-hover:text-pink-700">Attach File</span>
                    <span className="text-sm text-gray-500 mt-1">Add documents</span>
                    <input
                      type="file"
                      onChange={handleFileUpload}
                      className="hidden"
                    />
                  </label>
                </div>
                {formData.attachments.length > 0 && (
                  <div className="p-6 bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl border-2 border-gray-200 shadow-lg">
                    <div className="text-lg font-bold text-gray-700 mb-4 flex items-center">
                      <Upload className="w-5 h-5 mr-2 text-gray-500" />
                      Attachments ({formData.attachments.length}) 📎
                    </div>
                    <div className="space-y-3">
                      {formData.attachments.map((file, index) => (
                        <div key={index} className="flex items-center p-4 bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                          <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center mr-4">
                            <Upload className="w-6 h-6 text-white" />
                          </div>
                          <div className="flex-1">
                            <div className="font-semibold text-gray-700">{file.name}</div>
                            <div className="text-sm text-gray-500">{(file.size / 1024).toFixed(1)} KB</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Submit Buttons */}
              <div className="flex justify-end space-x-6 pt-8 border-t-2 border-gradient-to-r from-purple-200 to-pink-200">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-8 py-4 border-2 border-gray-300 text-gray-700 rounded-2xl hover:bg-gray-50 hover:border-gray-400 transition-all duration-300 font-bold text-lg shadow-lg hover:shadow-xl"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-12 py-4 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white rounded-2xl hover:from-blue-600 hover:via-purple-600 hover:to-pink-600 transition-all duration-300 font-bold text-lg shadow-2xl hover:shadow-3xl transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <div className="w-6 h-6 border-3 border-white border-t-transparent rounded-full animate-spin mr-3"></div>
                      Sending Recognition... ✨
                    </>
                  ) : (
                    <>
                      <Send className="w-6 h-6 mr-3" />
                      Send Recognition 🚀
                    </>
                  )}
                </button>
              </div>
            </form>
          </>
        )}
      </div>

      {/* Emoji Picker */}
      {showEmojiPicker && (
        <EmojiPicker
          onEmojiSelect={handleEmojiSelect}
          onClose={() => setShowEmojiPicker(false)}
        />
      )}
    </div>
  )
}

export default GiveRecognitionModal