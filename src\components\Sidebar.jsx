import {
  Home,
  Users,
  Award,
  Users2,
  Bar<PERSON>hart3,
  Calendar,
  Image,
  Trophy,
  Lightbulb,
  BookOpen
} from 'lucide-react'
import './Sidebar.css'

const Sidebar = () => {
  const menuItems = [
    { icon: Home, label: 'News Feed', active: false },
    { icon: Users, label: 'Profiles', active: false },
    { icon: Award, label: 'Recognition', active: true, badge: '2' },
    { icon: Users2, label: 'Groups', active: false },
    { icon: BarChart3, label: 'Polls', active: false },
    { icon: Calendar, label: 'Events', active: false, badge: '1' },
    { icon: Image, label: 'Media', active: false },
    { icon: Trophy, label: 'Leaderboard', active: false },
    { icon: Lightbulb, label: 'Spotlights', active: false },
    { icon: BookOpen, label: 'HR Blog', active: false },
  ]

  return (
    <div className="sidebar">
      {/* Header */}
      <div className="sidebar-header">
        <h1 className="sidebar-title">HR Social Hub</h1>
      </div>

      {/* Navigation Menu */}
      <nav className="sidebar-nav">
        <ul className="sidebar-menu">
          {menuItems.map((item, index) => (
            <li key={index}>
              <a
                href="#"
                className={`sidebar-item ${item.active ? 'active' : ''}`}
              >
                <item.icon className="sidebar-item-icon" />
                <span className="sidebar-item-label">{item.label}</span>
                {item.badge && (
                  <span className="sidebar-badge">
                    {item.badge}
                  </span>
                )}
              </a>
            </li>
          ))}
        </ul>
      </nav>

      {/* Profile Section */}
      <div className="sidebar-profile">
        <div className="sidebar-profile-content">
          <div className="sidebar-avatar">
            <span className="sidebar-avatar-text">JD</span>
          </div>
          <div className="sidebar-profile-info">
            <p className="sidebar-profile-name">John Doe</p>
            <p className="sidebar-profile-title">Product Designer</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Sidebar
